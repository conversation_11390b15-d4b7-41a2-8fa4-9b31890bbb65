import 'package:dio/dio.dart';
import '../models/nsl_java_solution_model.dart';
import 'base_api_service.dart';
import '../utils/logger.dart';

/// Service for handling NSL Java Solution API operations
class NslJavaSolutionService extends BaseApiService {
  static const String _baseUrl = 'http://10.26.1.52:8084';
  static const String _activeSolutionsEndpoint = '/api/global-objectives/active';

  /// Fetch all active global objectives/solutions
  Future<NslJavaSolutionModel> getActiveGlobalObjectives() async {
    try {
      Logger.info('Fetching active global objectives from NSL Java Solution API');

      // Override the base URL for this specific request
      final fullUrl = '$_baseUrl$_activeSolutionsEndpoint';

      final response = await dio.get(fullUrl);

      Logger.info('Active global objectives fetched successfully: ${response.statusCode}');
      Logger.info('Response data type: ${response.data.runtimeType}');
      Logger.info('Response data: ${response.data}');

      if (response.data != null) {
        // Handle different response formats
        if (response.data is List) {
          // If the response is directly a list of solutions
          return NslJavaSolutionModel(
            data: List<NslJavaSolution>.from(
              response.data.map((x) => NslJavaSolution.fromJson(x))
            ),
            count: response.data.length,
            message: "Direct list response",
          );
        } else if (response.data is Map<String, dynamic>) {
          // If the response is wrapped in an object
          return NslJavaSolutionModel.fromJson(response.data);
        } else {
          Logger.error('Unexpected response format: ${response.data}');
          throw Exception('Invalid response format');
        }
      } else {
        Logger.error('Empty response received');
        throw Exception('Empty response');
      }
    } catch (e) {
      Logger.error('Error fetching active global objectives: $e');
      rethrow;
    }
  }

  /// Fetch active global objectives with error handling and fallback
  Future<NslJavaSolutionModel> getActiveGlobalObjectivesWithFallback() async {
    try {
      return await getActiveGlobalObjectives();
    } catch (e) {
      Logger.error('Primary API call failed, returning empty model: $e');
      // Return empty model as fallback
      return NslJavaSolutionModel(
        data: [],
        count: 0,
        message: "Error occurred",
        success: false,
      );
    }
  }

  /// Fetch a specific global objective by ID
  Future<NslJavaSolution?> getGlobalObjectiveById(String goid) async {
    try {
      Logger.info('Fetching global objective by ID: $goid');

      final solutions = await getActiveGlobalObjectives();
      
      if (solutions.data != null) {
        for (var solution in solutions.data!) {
          if (solution.goid == goid) {
            return solution;
          }
        }
      }

      Logger.info('Global objective with ID $goid not found');
      return null;
    } catch (e) {
      Logger.error('Error fetching global objective by ID: $e');
      rethrow;
    }
  }

  /// Search global objectives by name
  Future<List<NslJavaSolution>> searchGlobalObjectivesByName(String searchTerm) async {
    try {
      Logger.info('Searching global objectives by name: $searchTerm');

      final solutions = await getActiveGlobalObjectives();
      
      if (solutions.data != null) {
        return solutions.data!
            .where((solution) => 
                solution.name != null && 
                solution.name!.toLowerCase().contains(searchTerm.toLowerCase()))
            .toList();
      }

      return [];
    } catch (e) {
      Logger.error('Error searching global objectives by name: $e');
      rethrow;
    }
  }

  /// Get filtered solutions based on status
  Future<List<NslJavaSolution>> getGlobalObjectivesByStatus(String status) async {
    try {
      Logger.info('Fetching global objectives by status: $status');

      final solutions = await getActiveGlobalObjectives();
      
      if (solutions.data != null) {
        return solutions.data!
            .where((solution) => 
                solution.status != null && 
                solution.status!.toLowerCase() == status.toLowerCase())
            .toList();
      }

      return [];
    } catch (e) {
      Logger.error('Error fetching global objectives by status: $e');
      rethrow;
    }
  }
}
