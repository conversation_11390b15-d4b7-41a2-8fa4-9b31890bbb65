// To parse this JSON data, do
//
//     final nslJavaSolutionModel = nslJavaSolutionModelFromJson(jsonString);

import 'dart:convert';

NslJavaSolutionModel nslJavaSolutionModelFromJson(String str) => 
    NslJavaSolutionModel.fromJson(json.decode(str));

String nslJavaSolutionModelToJson(NslJavaSolutionModel data) => 
    json.encode(data.toJson());

class NslJavaSolutionModel {
  int? count;
  String? message;
  bool? success;
  List<NslJavaSolution>? data;

  NslJavaSolutionModel({
    this.count,
    this.message,
    this.success,
    this.data,
  });

  NslJavaSolutionModel copyWith({
    int? count,
    String? message,
    bool? success,
    List<NslJavaSolution>? data,
  }) =>
      NslJavaSolutionModel(
        count: count ?? this.count,
        message: message ?? this.message,
        success: success ?? this.success,
        data: data ?? this.data,
      );

  factory NslJavaSolutionModel.fromJson(Map<String, dynamic> json) => 
      NslJavaSolutionModel(
        count: json["count"],
        message: json["message"],
        success: json["success"],
        data: json["data"] == null 
            ? [] 
            : List<NslJavaSolution>.from(
                json["data"]!.map((x) => NslJavaSolution.fromJson(x))
              ),
      );

  Map<String, dynamic> toJson() => {
        "count": count,
        "message": message,
        "success": success,
        "data": data == null 
            ? [] 
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class NslJavaSolution {
  int? autoId;
  String? goId;
  String? name;
  String? version;
  String? status;
  String? description;
  String? createdAt;
  String? updatedAt;
  String? tenantId;
  String? lastUsed;
  bool? deletedMark;
  String? versionType;
  String? primaryEntity;
  String? classification;
  String? tenantName;
  String? bookId;
  String? bookName;
  String? chapterId;
  String? chapterName;
  String? createdBy;
  String? updatedBy;
  String? naturalLanguage;

  NslJavaSolution({
    this.autoId,
    this.goId,
    this.name,
    this.version,
    this.status,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.tenantId,
    this.lastUsed,
    this.deletedMark,
    this.versionType,
    this.primaryEntity,
    this.classification,
    this.tenantName,
    this.bookId,
    this.bookName,
    this.chapterId,
    this.chapterName,
    this.createdBy,
    this.updatedBy,
    this.naturalLanguage,
  });

  // Getter for backward compatibility
  String? get goid => goId;

  NslJavaSolution copyWith({
    int? autoId,
    String? goId,
    String? name,
    String? version,
    String? status,
    String? description,
    String? createdAt,
    String? updatedAt,
    String? tenantId,
    String? lastUsed,
    bool? deletedMark,
    String? versionType,
    String? primaryEntity,
    String? classification,
    String? tenantName,
    String? bookId,
    String? bookName,
    String? chapterId,
    String? chapterName,
    String? createdBy,
    String? updatedBy,
    String? naturalLanguage,
  }) =>
      NslJavaSolution(
        autoId: autoId ?? this.autoId,
        goId: goId ?? this.goId,
        name: name ?? this.name,
        version: version ?? this.version,
        status: status ?? this.status,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        tenantId: tenantId ?? this.tenantId,
        lastUsed: lastUsed ?? this.lastUsed,
        deletedMark: deletedMark ?? this.deletedMark,
        versionType: versionType ?? this.versionType,
        primaryEntity: primaryEntity ?? this.primaryEntity,
        classification: classification ?? this.classification,
        tenantName: tenantName ?? this.tenantName,
        bookId: bookId ?? this.bookId,
        bookName: bookName ?? this.bookName,
        chapterId: chapterId ?? this.chapterId,
        chapterName: chapterName ?? this.chapterName,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory NslJavaSolution.fromJson(Map<String, dynamic> json) => 
      NslJavaSolution(
        autoId: json["autoId"],
        goId: json["goId"],
        name: json["name"],
        version: json["version"],
        status: json["status"],
        description: json["description"],
        createdAt: json["createdAt"],
        updatedAt: json["updatedAt"],
        tenantId: json["tenantId"],
        lastUsed: json["lastUsed"],
        deletedMark: json["deletedMark"],
        versionType: json["versionType"],
        primaryEntity: json["primaryEntity"],
        classification: json["classification"],
        tenantName: json["tenantName"],
        bookId: json["bookId"],
        bookName: json["bookName"],
        chapterId: json["chapterId"],
        chapterName: json["chapterName"],
        createdBy: json["createdBy"],
        updatedBy: json["updatedBy"],
        naturalLanguage: json["naturalLanguage"],
      );

  Map<String, dynamic> toJson() => {
        "autoId": autoId,
        "goId": goId,
        "name": name,
        "version": version,
        "status": status,
        "description": description,
        "createdAt": createdAt,
        "updatedAt": updatedAt,
        "tenantId": tenantId,
        "lastUsed": lastUsed,
        "deletedMark": deletedMark,
        "versionType": versionType,
        "primaryEntity": primaryEntity,
        "classification": classification,
        "tenantName": tenantName,
        "bookId": bookId,
        "bookName": bookName,
        "chapterId": chapterId,
        "chapterName": chapterName,
        "createdBy": createdBy,
        "updatedBy": updatedBy,
        "naturalLanguage": naturalLanguage,
      };
}
