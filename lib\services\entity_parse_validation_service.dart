import 'package:dio/dio.dart';
import 'package:nsl/config/environment.dart';
import 'package:nsl/models/parse_validation_entity/parse_validation_entity_model.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/services/dio_client.dart';
import 'package:nsl/utils/logger.dart';

class EntityParseValidationService {
  final Dio _dio = DioClient().client;

  // Base URL
  late final String _baseUrl;

  late final String parseValidateEntities;

  // Singleton instance
  static final EntityParseValidationService _instance =
      EntityParseValidationService._internal();

  // Factory constructor
  factory EntityParseValidationService() => _instance;

  final authService = AuthService();

  // Internal constructor
  EntityParseValidationService._internal() {
    // Initialize base URL from environment
    _baseUrl = Environment.validateBaseUrl;
    parseValidateEntities =
        '$_baseUrl/api/entities/parse-and-validate/entities';
  }

  // Create a new conversation
  Future<ParseValidationEntityModel?> parseValidateEntity(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      // Prepare request payload
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
    return null;
  }
}
