import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

class BooleanWidget extends StatefulWidget {
  final bool initialValue;
  final Color activeColor;
  final Color inactiveColor;
  final Color? thumbColor;
  final Color? trackColor;
  final double width;
  final double height;
  final String? label;
  final String? description;
  final bool showStatusText;
  final TextAlign labelPosition;
  final bool isRounded;
  final bool enabled;
  final ValueChanged<bool>? onChanged;
  final bool? testValue; // For testing purposes only

  const BooleanWidget({
    super.key,
    this.initialValue = false,
    this.activeColor = const Color(0xFF0058FF),
    this.inactiveColor = Colors.grey,
    this.thumbColor,
    this.trackColor,
    this.width = 60.0,
    this.height = 30.0,
    this.label,
    this.description,
    this.showStatusText = false,
    this.labelPosition = TextAlign.start,
    this.isRounded = true,
    this.enabled = true,
    this.onChanged,
    this.testValue,
  });

  /// Creates a BooleanWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the BooleanWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": true,
  ///   "activeColor": "#0058FF",
  ///   "inactiveColor": "grey",
  ///   "thumbColor": "white",
  ///   "trackColor": "#E0E0E0",
  ///   "width": 60.0,
  ///   "height": 30.0,
  ///   "label": "Mode",
  ///   "description": "Dark mode",
  ///   "showStatusText": false,
  ///   "labelPosition": "start",
  ///   "isRounded": true,
  ///   "enabled": true
  /// }
  /// ```
  factory BooleanWidget.fromJson(Map<String, dynamic> json) {
    // Parse label position
    TextAlign labelPosition = TextAlign.start;
    if (json['labelPosition'] != null) {
      switch (json['labelPosition'].toString().toLowerCase()) {
        case 'end':
        case 'right':
        case 'trailing':
          labelPosition = TextAlign.end;
          break;
        case 'start':
        case 'left':
        case 'leading':
          labelPosition = TextAlign.start;
          break;
      }
    }

    return BooleanWidget(
      initialValue: json['initialValue'] ?? false,
      activeColor:
          _colorFromJson(json['activeColor']) ?? const Color(0xFF0058FF),
      inactiveColor: _colorFromJson(json['inactiveColor']) ?? Colors.grey,
      thumbColor: _colorFromJson(json['thumbColor']),
      trackColor: _colorFromJson(json['trackColor']),
      width: (json['width'] as num?)?.toDouble() ?? 50.0,
      height: (json['height'] as num?)?.toDouble() ?? 28.0,
      label: json['label'] as String?,
      description: json['description'] as String?,
      showStatusText: json['showStatusText'] ?? false,
      labelPosition: labelPosition,
      isRounded: json['isRounded'] ?? true,
      enabled: json['enabled'] ?? true,
      onChanged: (value) {
        // This would be handled by the app in a real implementation
      },
    );
  }

  /// Converts the BooleanWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    String labelPositionString = 'start';
    if (labelPosition == TextAlign.end) {
      labelPositionString = 'end';
    }

    return {
      'initialValue': initialValue,
      'activeColor': _colorToJson(activeColor),
      'inactiveColor': _colorToJson(inactiveColor),
      if (thumbColor != null) 'thumbColor': _colorToJson(thumbColor!),
      if (trackColor != null) 'trackColor': _colorToJson(trackColor!),
      'width': width,
      'height': height,
      if (label != null) 'label': label,
      if (description != null) 'description': description,
      'showStatusText': showStatusText,
      'labelPosition': labelPositionString,
      'isRounded': isRounded,
      'enabled': enabled,
    };
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors - return MaterialColor for standard colors
      // to ensure round-trip conversion works correctly
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return const Color(0xFF0058FF);
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle the custom color #0058FF
    if (color.value == 0xFF0058FF) return '#0058FF';

    // Handle standard colors by name for better readability and test compatibility
    if (color == Colors.red) return 'red';
    if (color == Color(0xFF0058FF)) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      // We'll keep the original MaterialColor in the round-trip conversion
      // by using a special format that our fromJson method can recognize
      if (color == Colors.red) return 'red';
      if (color == Color(0xFF0058FF)) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final r = (color.r * 255).round().toRadixString(16).padLeft(2, '0');
    final g = (color.g * 255).round().toRadixString(16).padLeft(2, '0');
    final b = (color.b * 255).round().toRadixString(16).padLeft(2, '0');

    return '#$r$g$b';
  }

  @override
  BooleanWidgetState createState() => BooleanWidgetState();
}

class BooleanWidgetState extends State<BooleanWidget>
    with TickerProviderStateMixin {
  late bool _isTrue;
  bool _isHovered = false;

  // Animation controller for hover effects
  late AnimationController _hoverAnimationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller for hover effects
    _hoverAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    // Create animation for potential future hover effects (no scaling to maintain consistent thumb size)
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.0, // No scaling to maintain consistent thumb size
    ).animate(
      CurvedAnimation(
        parent: _hoverAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Use test value if provided (for testing purposes)
    if (widget.testValue != null) {
      _isTrue = widget.testValue!;
    } else {
      _isTrue = widget.initialValue;
    }
  }

  @override
  void dispose() {
    _hoverAnimationController.dispose();
    super.dispose();
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create the switch/toggle widget with a fixed thumb size for all states
    Widget toggleWidget = MouseRegion(
      onEnter: widget.enabled
          ? (_) {
              setState(() => _isHovered = true);
              _hoverAnimationController.forward();
            }
          : null,
      onExit: widget.enabled
          ? (_) {
              setState(() => _isHovered = false);
              _hoverAnimationController.reverse();
            }
          : null,
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: Switch(
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          trackOutlineColor: WidgetStateProperty.resolveWith(
            (states) => Colors.transparent,
          ),
          thumbColor: WidgetStateProperty.resolveWith((states) {
            if (!widget.enabled) {
              return Colors.white;
            }
            return widget.thumbColor ?? Colors.white;
          }),
          trackColor: WidgetStateProperty.resolveWith((states) {
            if (!widget.enabled) {
              if (states.contains(WidgetState.selected)) {
                return Colors.grey.shade400;
              }
              return Colors.grey.shade300;
            }
            if (states.contains(WidgetState.selected)) {
              return widget.activeColor;
            }
            return widget.trackColor ?? Colors.grey.shade600;
          }),
          thumbIcon: WidgetStateProperty.all(
            Icon(
              Icons.circle,
              size: widget.height * 0.7, // fixed thumb size
              color: widget.thumbColor ?? Colors.white,
            ),
          ),
          value: _isTrue,
          onChanged: widget.enabled
              ? (bool value) {
                  setState(() {
                    _isTrue = value;
                  });
                  widget.onChanged?.call(value);
                }
              : null,
        ),
      ),
    );

    // Create the label if provided
    Widget? labelWidget;
    // if (widget.label != null) {
    //   labelWidget = Text(
    //     widget.label!,
    //     style: TextStyle(
    //       fontSize: _getResponsiveValueFontSize(context),
    //       fontWeight: FontWeight.w500,
    //       fontFamily: 'Inter',
    //       color: widget.enabled ? Colors.black87 : Colors.grey.shade500,
    //     ),
    //   );
    // }

    // Create the description if provided
    Widget? descriptionWidget;
    if (widget.description != null) {
      descriptionWidget = Text(
        widget.description!,
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontSize: _getResponsiveFontSize(context),
          fontWeight: FontManager.medium,
          color: widget.enabled ? Colors.black87 : Colors.grey.shade500,
        ),
      );
    }

    // Optimized layout for left alignment with minimal spacing
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Switch on the left
            toggleWidget,
            if (widget.description != null) ...[
              const SizedBox(width: 4),
              // Text content without Expanded to avoid extra spacing
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (labelWidget != null) labelWidget,
                  if (descriptionWidget != null) descriptionWidget,
                ],
              ),
            ],
          ],
        ),
      ],
    );
  }
}
