import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/models/workflow.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web_transaction/web_transaction_execution.dart';
import 'package:nsl/screens/web_transaction/web_transaction_widgets_demo.dart';
import 'package:nsl/screens/web_transaction/workflow_transaction_screen.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/flex_mapper.dart';
import 'package:nsl/widgets/input_field_widget.dart';
import 'package:reorderables/reorderables.dart';

import 'package:flutter/services.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../utils/widget_factory.dart' as utils_widget_factory;

class WebSolutionWidgets extends StatefulWidget {
  WebSolutionWidgets(
      {super.key,
      this.isPadding = false,
      this.isChatEnabled = false,
      this.isExpanded = false,
      this.onExpansionChange,
      this.showBackNavigation = true,
      this.showToggleControls = false});
  bool isPadding;
  bool isChatEnabled;
  bool isExpanded;
  bool showBackNavigation;
  bool showToggleControls;
  Function? onExpansionChange;

  @override
  State<WebSolutionWidgets> createState() => _WebSolutionWidgetsState();
}

class _WebSolutionWidgetsState extends State<WebSolutionWidgets>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final List<String> _messages = [];
  String objectiveId = '';
  String objectiveName = '';

  // JSON data variables
  Map<String, dynamic>? jsonData;
  Map<String, dynamic>? solutionWidgetsData;
  List<Map<String, dynamic>> optionButtonsData = [];
  List<Map<String, dynamic>> recommendationData = [];
  List<Map<String, dynamic>> actionButtonsData = [];
  List<Map<String, dynamic>>? userInterfaceData;

  // Animation controllers
  late AnimationController _slideAnimationController;
  late Animation<Offset> _slideAnimation;

  // State management
  bool _isTransitioning = false;
  bool _hasTransitioned = false;

  // Collection data for the back navigation - will be loaded from JSON
  Map<String, dynamic> _collectionData = {
    "name": "Solution Widgets",
    "backImage": "assets/images/my_business/back_arrow.svg"
  };

  bool isRightSectionExpanded = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadJsonData();
  }

  void _initializeAnimations() {
    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -1.0),
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _slideAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadJsonData() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    objectiveId = prefs.getString("solution_go_id") ?? '';
    objectiveName = prefs.getString("solution_go_name") ?? '';

    try {
      // Determine which JSON to load based on transition state
      final String jsonFile = _hasTransitioned
          ? 'assets/data/solution_lo_one.json'
          : 'assets/data/solution_lo_two.json';

      final String solutionJsonString = await rootBundle.loadString(jsonFile);
      final Map<String, dynamic> solutionData = jsonDecode(solutionJsonString);

      // Load solution_components.json for fallback UI
      // final String jsonString =
      //     await rootBundle.loadString('assets/data/solution_components.json');
      // final Map<String, dynamic> data = jsonDecode(jsonString);

      // Load solution_widgets.json for side panel
      // final String solutionWidgetsJsonString =
      //     await rootBundle.loadString('assets/data/solution_widgets.json');
      // final Map<String, dynamic> solutionWidgetsJsonData =
      //     jsonDecode(solutionWidgetsJsonString);

      setState(() {
        jsonData = solutionData;
        // solutionWidgetsData = solutionWidgetsJsonData;

        // Extract lead creation data based on transition state
        final String dataKey = _hasTransitioned
            ? 'lead_creation_lo2_information_hierarchy'
            : 'lead_creation_lo1_information_hierarchy';

        final leadCreationData = solutionData[dataKey];

        if (leadCreationData != null) {
          // Use lead creation data for WidgetComponent
          // userInterfaceData = leadCreationData;
          userInterfaceData = List<Map<String, dynamic>>.from(
              leadCreationData["physical_layer"]);

          // Handle RecommendationBox data based on transition state
          if (_hasTransitioned) {
            // Load "opportunity_health_check" from solution_lo_one.json
            final level2Context =
                leadCreationData['level_2_contextual_information'];
            final chatAreaCritical = level2Context?['chat_area_critical'];
            final opportunityHealthCheck =
                chatAreaCritical?['opportunity_health_check'];

            if (opportunityHealthCheck != null) {
              // Create a single consolidated card with all opportunity health data
              final title =
                  opportunityHealthCheck['title'] ?? '📈 Opportunity Health';
              final healthScore = opportunityHealthCheck['health_score'] ??
                  '82/100 - Strong Opportunity';

              // Build content list for the single card
              List<String> cardContent = [];
              cardContent.add('$healthScore\n');

              // Extract health factors
              final healthFactors =
                  opportunityHealthCheck['health_factors'] as List<dynamic>?;
              if (healthFactors != null) {
                for (var factor in healthFactors) {
                  cardContent.add(factor.toString());
                }
              }

              // Add improvement recommendations
              final improvements =
                  opportunityHealthCheck['improvement_recommendations']
                      as List<dynamic>?;
              if (improvements != null) {
                cardContent.add('\nImprovement Recommendations:');
                for (var improvement in improvements) {
                  cardContent.add('💡 ${improvement.toString()}');
                }
              }

              recommendationData = [
                {
                  'type': 'card',
                  'title': title,
                  'content': cardContent,
                  'icon': '📈',
                }
              ];
            } else {
              // Fallback if opportunity_health_check is not found
              recommendationData = [
                {
                  'type': 'card',
                  'title': '📈 Opportunity Health',
                  'content': [
                    '82/100 - Strong Opportunity\n',
                    '✅ Budget: Likely allocated (based on funding)',
                    '⚠️ Authority: Partial - need to identify final decision maker',
                    '✅ Need: Strong pain points identified',
                    '✅ Timeline: Reasonable 3-6 month window',
                    '\nImprovement Recommendations:',
                    '💡 Identify CFO or budget holder',
                    '💡 Map complete stakeholder network',
                    '💡 Quantify business impact/ROI',
                  ],
                  'icon': '📈',
                }
              ];
            }
          } else {
            // Extract chat_area_critical data for RecommendationBox (original logic)
            final level2Context =
                leadCreationData['level_2_contextual_information'];
            final chatAreaCritical = level2Context?['chat_area_critical'];

            if (chatAreaCritical != null) {
              // Map company verification and territory fit cards to recommendationData
              final companyCard = chatAreaCritical['company_verification_card'];
              final territoryCard = chatAreaCritical['territory_fit_card'];

              recommendationData = [];

              if (companyCard != null) {
                recommendationData.add({
                  'type': 'card',
                  'title': companyCard['title'] ?? '✅ Company Verified',
                  'content': companyCard['content'] ?? [],
                  'action': companyCard['action'] ?? 'View full profile →',
                  'icon': '✅',
                });
              }

              if (territoryCard != null) {
                recommendationData.add({
                  'type': 'card',
                  'title': territoryCard['title'] ?? '🗺️ Territory Alignment',
                  'content': territoryCard['content'] ?? [],
                  'action':
                      territoryCard['action'] ?? 'View territory details →',
                  'icon': '🗺️',
                });
              }
            }
          }

          // Extract essential inputs for optionButtonsData
          final level1Primary = leadCreationData['level_1_primary_actions'];
          final essentialInputs = level1Primary?['essential_inputs'];

          if (essentialInputs != null) {
            optionButtonsData = [
              {
                'type': 'card',
                'icon': 'assets/images/my_business/box_add.svg',
                'label': 'Verify Company',
                'onTap': () {
                  print("Verify Company clicked");
                },
              },
              {
                'type': 'card',
                'icon': 'assets/images/my_business/box_add.svg',
                'label': 'Check Territory',
                'onTap': () {
                  print("Check Territory clicked");
                },
              },
              {
                'type': 'card',
                'icon': 'assets/images/my_business/box_add.svg',
                'label': 'AI Enrichment',
                'onTap': () {
                  print("AI Enrichment clicked");
                },
              },
            ];
          }

          // Extract action buttons from progression logic
          final progressionLogic = leadCreationData['progression_logic'];
          if (progressionLogic != null) {
            actionButtonsData = [
              {
                'text': 'Continue to Details →',
                'type': 'primary',
                'action': 'continue',
                'image': 'assets/images/my_business/solutions/send_sol.svg',
              },
              {
                'text': 'Save as Draft',
                'type': 'secondary',
                'action': 'save_draft',
                'image': 'assets/images/my_business/solutions/save_sol.svg',
              },
              {
                'text': 'Cancel',
                'type': 'secondary',
                'action': 'cancel',
                'image': 'assets/images/my_business/solutions/cancel_sol.svg',
              },
            ];
          }
        } else {
          // Fallback to original solution_components.json data
          _setDefaultData();
        }
      });

      print("JSON data loaded successfully");
    } catch (e) {
      print("Error loading JSON data: $e");
      // Fallback to default data
      _setDefaultData();
    }
  }

  void _setDefaultData() {
    optionButtonsData = [
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Check Policy',
        'onTap': () {
          print("Check Policy clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Add to Calendar',
        'onTap': () {
          print("Add to Calendar clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Use AI Suggestion',
        'onTap': () {
          print("AI Suggestion clicked");
        },
      },
    ];

    recommendationData = [
      {'type': 'text', 'value': '2-Day Overlap With John (Lead Developer)'},
      {'type': 'text', 'value': 'Manager Is Available For Approval All Week'},
      {
        'type': 'text',
        'value': 'Youll Have 12 Days Remaining After This Request'
      },
    ];
  }

  void _handleSendMessage() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _messages.add(text);
        _textController.clear();
      });

      // Trigger transition if not already transitioned
      if (!_hasTransitioned && !_isTransitioning) {
        _triggerTransition();
      }
    }
  }

  Future<void> _triggerTransition() async {
    if (_isTransitioning) return;

    setState(() {
      _isTransitioning = true;
    });

    // Start slide-up animation
    await _slideAnimationController.forward();

    // Update state and reload data
    setState(() {
      _hasTransitioned = true;
      _isTransitioning = false;
    });

    // Reload JSON data with new state
    await _loadJsonData();

    // Reset animation for next use
    _slideAnimationController.reset();
  }

  // Build fixed header with back navigation and LO progress
  Widget _buildFixedHeader() {
    // Determine current step and LO title based on transition state
    final currentStep = _hasTransitioned ? 2 : 1;
    final totalSteps = 2;
    final loTitle = _hasTransitioned
        ? "Lead Qualification & Opportunity Development"
        : "Lead Basics & Discovery";

    return Container(
      width: double.infinity,
      padding: widget.isPadding
          ? EdgeInsets.only(
              left: AppSpacing.md,
              right: AppSpacing.md,
              // top: AppSpacing.sm,
              // bottom: AppSpacing.sm
            )
          : EdgeInsets.only(
              left: AppSpacing.md,
              right: AppSpacing.md,
              top: AppSpacing.sm,
              bottom: AppSpacing.sm),
      decoration: BoxDecoration(
        color: const Color(0xffF7F9FB),
      ),
      child: Row(
        children: [
          // Left side - Back navigation (conditionally shown)
          if (widget.showBackNavigation) _buildBackNavigation(),

          // Spacer to push content to the right
          Expanded(child: Container()),

          // Right side - LO title and progress
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // LO Title
              Text(
                "LO TITLE",
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s10,
                  fontWeight: FontManager.medium,
                  color: Colors.grey.shade600,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),

              const SizedBox(width: AppSpacing.sm),

              // Progress indicator
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Step $currentStep/$totalSteps",
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                      fontFamily: FontManager.fontFamilyInter,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.xs),

                  // Progress bar
                  Container(
                    width: 80,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: currentStep / totalSteps,
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF0058FF),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(width: AppSpacing.md),
          if (widget.showToggleControls)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Expand Collection button - Fullscreen
                Container(
                  width: 32,
                  height: 32,
                  padding: EdgeInsets.only(
                    top: AppSpacing.sm,
                  ),
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          widget.isExpanded = !widget.isExpanded;
                        });
                        if (widget.onExpansionChange != null) {
                          widget.onExpansionChange!();
                        }
                      },
                      child: Center(
                        child: CustomImage.asset(
                          "assets/images/my_business/expand_collection.svg",
                          width: 18,
                          height: 18,
                        ).toWidget(),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: AppSpacing.md),
                Container(
                  padding: EdgeInsets.only(
                    top: AppSpacing.sm,
                    right: AppSpacing.md,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: 20,
                        width: 25,
                        child: Transform.scale(
                          scale: 0.6,
                          child: Switch.adaptive(
                            value: widget.isChatEnabled,
                            activeColor: Colors.white,
                            activeTrackColor: Color(0xff0058FF),
                            inactiveThumbColor: Colors.white,
                            inactiveTrackColor: Colors.grey[300],
                            trackOutlineColor:
                                WidgetStateProperty.all(Colors.transparent),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            onChanged: (value) {
                              setState(() {
                                widget.isChatEnabled = value;
                              });

                              WorkflowTransactionScreen.toggleChatInput(
                                  widget.isChatEnabled);

                              if (widget.isChatEnabled) {
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.webTransactionWidgetsDemo;
                              } else {
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.webCollectionModuleDemo;
                              }
                            },
                          ),
                        ),
                      ),
                      Text(
                        widget.isChatEnabled ? "NSL" : "Normal",
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.black,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          _HoverExpandButton(
            isExpanded: isRightSectionExpanded,
            tooltipMessage:
                isRightSectionExpanded ? 'Collapse panel' : 'Expand panel',
            onTap: () {
              setState(() {
                isRightSectionExpanded = !isRightSectionExpanded;
              });
            },
          ),
        ],
      ),
    );
  }

  // Build back navigation element with arrow and text
  Widget _buildBackNavigation() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          final provider = Provider.of<WebHomeProvider>(context, listen: false);
          provider.selectedSolutionName = "";
          if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
              provider.solutionWidgetsSelectedFrom ==
                  ScreenConstants.myBusinessHome) {
            provider.currentScreenIndex = ScreenConstants.myBusinessHome;
            provider.solutionWidgetsSelectedFrom = "";
          } else if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
              provider.solutionWidgetsSelectedFrom ==
                  ScreenConstants.myBusinessSolutions) {
            provider.currentScreenIndex = ScreenConstants.myBusinessSolutions;
            provider.solutionWidgetsSelectedFrom = "";
          }
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Back arrow icon from JSON
            CustomImage.asset(
              _collectionData['backImage'],
              width: 10,
              height: 10,
              color: Colors.black,
            ).toWidget(),
            SizedBox(width: AppSpacing.sm),

            // Collection name text from JSON
            Text(
              "Back",
              // Provider.of<WebHomeProvider>(context, listen: false)
              //         .selectedSolutionName
              //         .isNotEmpty
              //     ? Provider.of<WebHomeProvider>(context, listen: false)
              //         .selectedSolutionName
              //     : _collectionData['name'],
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.regular,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final String latestMessage = _messages.isNotEmpty ? _messages.last : "";

    return Scaffold(
        backgroundColor: const Color(0xffF7F9FB),
        body: objectiveId.isEmpty
            ? Container()
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: AppSpacing.sm, horizontal: AppSpacing.sm),
                    child: _buildBackNavigation(),
                  ),
                  Expanded(
                    child: WebTransactionExecution(
                      objectiveId: objectiveId,
                      objectiveName: objectiveName,
                      // isLeftSideExpanded: _isExpanded,
                    ),
                  ),
                ],
              )


        );
  }

  // Check if right section has data available

}

class WidgetComponent extends StatefulWidget {
  final List<Map<String, dynamic>>? uiData;

  const WidgetComponent({super.key, this.uiData});

  @override
  State<WidgetComponent> createState() => _WidgetComponentState();
}

class _WidgetComponentState extends State<WidgetComponent> {
  // Controllers for text fields
  final TextEditingController _contactNameController = TextEditingController();
  final TextEditingController _contactEmailController = TextEditingController();
  final TextEditingController _contactPhoneController = TextEditingController();
  final TextEditingController _inquirySummaryController =
      TextEditingController();
  final Map<String, GlobalKey<InputFieldWidgetState>> _inputFieldKeys = {};

  // Dropdown values
  String? _selectedJobTitle;
  @override
  void dispose() {
    _contactNameController.dispose();
    _contactEmailController.dispose();
    _contactPhoneController.dispose();
    _inquirySummaryController.dispose();
    super.dispose();
  }

  // Helper method to convert JSON text style to Flutter TextStyle
  TextStyle? _getTextStyleFromJson(Map<String, dynamic>? textStyleJson) {
    if (textStyleJson == null) return null;

    // Parse font size from JSON
    double fontSize = 12.0;
    if (textStyleJson['fontsize'] != null) {
      fontSize = (textStyleJson['fontsize'] as num).toDouble();
    }

    // Parse font weight from JSON
    FontWeight fontWeight = FontManager.medium;
    if (textStyleJson['fontweight'] != null) {
      final fontWeightStr = textStyleJson['fontweight'] as String;
      switch (fontWeightStr) {
        case 'w100':
          fontWeight = FontWeight.w100;
          break;
        case 'w200':
          fontWeight = FontWeight.w200;
          break;
        case 'w300':
          fontWeight = FontWeight.w300;
          break;
        case 'w400':
          fontWeight = FontWeight.w400;
          break;
        case 'w500':
          fontWeight = FontWeight.w500;
          break;
        case 'w600':
          fontWeight = FontWeight.w600;
          break;
        case 'w700':
          fontWeight = FontWeight.w700;
          break;
        case 'w800':
          fontWeight = FontWeight.w800;
          break;
        case 'w900':
          fontWeight = FontWeight.w900;
          break;
        default:
          fontWeight = FontManager.medium;
      }
    }

    // Parse color from JSON
    Color textColor = Colors.black;
    if (textStyleJson['color'] != null) {
      final colorStr = textStyleJson['color'] as String;
      switch (colorStr.toLowerCase()) {
        case 'blue':
          textColor = Colors.blue;
          break;
        case 'red':
          textColor = Colors.red;
          break;
        case 'green':
          textColor = Colors.green;
          break;
        case 'orange':
          textColor = Colors.orange;
          break;
        case 'purple':
          textColor = Colors.purple;
          break;
        case 'black':
          textColor = Colors.black;
          break;
        case 'white':
          textColor = Colors.white;
          break;
        case 'grey':
          textColor = Colors.grey.shade600;
          break;
        default:
          textColor = Colors.black;
      }
    }
    final fontFamily = textStyleJson["fontfamily"];

    return FontManager.getCustomStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: textColor,
      fontFamily: fontFamily ?? FontManager.fontFamilyInter,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Extract data from lead creation hierarchy
    // final level1Primary = widget.uiData?['level_1_primary_actions'];
    // final textualHierarchy = level1Primary?['textual_hierarchy'];
    // final primaryHeading = textualHierarchy?['primary_heading'];
    // final essentialInputs = level1Primary?['essential_inputs'];
    // final detailedQualificationInputs =
    //     level1Primary?['detailed_qualification_inputs'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: AppSpacing.xs,
      children: (widget.uiData ?? [])
          .map(
            (e) => buildWidget(e),
          )
          .toList(),
    );
  }

  Widget buildWidget(mapData) {
    if (mapData["user_input"] != null) {
      return uiWidget(mapData);
    } else {
      switch (mapData["type"]) {
        case "widget_1":
          return recommendationWidget(mapData);
        case "widget_2":
          // return Container();
          return optionWidget(mapData);
      }
      return Container();
    }
  }

  Widget recommendationWidget(mapData) {
    List<Widget> wrapWidgets =
        (mapData['header']['recommendation']['data'] as List<dynamic>)
            .map<Widget>((data) {
      return OptionComponent(data: {
        "icon": mapData['header']['recommendation']["image"],
        "label": data,
        "type": 'card'
      });
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppSpacing.sm),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                mapData['header']['title']['value'],
                style: _getTextStyleFromJson(
                        mapData?['header']['title']['style']) ??
                    FontManager.getCustomStyle(
                      color: Colors.black,
                      fontSize: MediaQuery.of(context).size.width > 1550
                          ? FontManager.s14
                          : FontManager.s12,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontManager.semiBold,
                    ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: wrapWidgets,
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget optionWidget(mapData) {
    List<Widget> widgets = (mapData['header']['options'] as List)
        .map<Widget>((e) => Column(
              // spacing : AppSpacing.sm,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        e['title']['value'] ?? '',
                        style: _getTextStyleFromJson(e['title']['style']) ??
                            FontManager.getCustomStyle(
                              fontSize: MediaQuery.of(context).size.width > 1550
                                  ? FontManager.s14
                                  : FontManager.s12,
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.medium,
                              color: Colors.black,
                            ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (e['data'] != null) ...[
                  ...((e['data'] as List<dynamic>).map<Widget>((contentItem) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        contentItem.toString(),
                        style: _getTextStyleFromJson(e['style']) ??
                            FontManager.getCustomStyle(
                              fontSize: MediaQuery.of(context).size.width > 1550
                                  ? FontManager.s13
                                  : FontManager.s10,
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.regular,
                              color: Colors.grey.shade700,
                            ),
                      ),
                    );
                  }).toList()),
                ],
                if (e['subtitle'] != null) ...[
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTap: () {
                      print("${e['subtitle']} clicked");
                    },
                    child: Text(
                      e['subtitle']['value'] ?? '',
                      style: _getTextStyleFromJson(e['subtitle']['style']) ??
                          FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.medium,
                            color: const Color(0xFF0058FF),
                          ),
                    ),
                  ),
                ],
                SizedBox(
                  height: AppSpacing.sm,
                )
              ],
            ))
        .toList();

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: Column(children: widgets),
    );
  }

  Widget uiWidget(mapData) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: AppSpacing.xs),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            mapData?['header']['title']['value'] ?? '',
            style:
                _getTextStyleFromJson(mapData?['header']['title']['style']) ??
                    FontManager.getCustomStyle(
                      fontSize: FontManager.s14,
                      fontWeight: FontManager.semiBold,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
          ),
          SizedBox(
            height: AppSpacing.xxs,
          ),
          if (mapData != null && mapData?["user_input"] != null)
            _buildFlexGrid(mapData!["user_input"])
          else
            const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildFlexGrid(List<dynamic> inputList) {
    List<Widget> rows = [];
    List<Widget> currentRow = [];
    int currentFlexSum = 0;

    for (int i = 0; i < inputList.length; i++) {
      final item = inputList[i];
      final uiControl = item['ui_control'] ?? 'text';
      final flex = FlexMapper.getFlexValueForControl(uiControl);

      // If adding this widget exceeds row flex, push current row and start new
      if (currentFlexSum + flex > FlexMapper.totalRowFlex) {
        rows.add(
          Padding(
            padding: const EdgeInsets.only(
                bottom: AppSpacing.sm), // spacing between rows
            child: Row(children: currentRow),
          ),
        );
        currentRow = [];
        currentFlexSum = 0;
      }

      // Determine padding based on position in row
      final isFirstInRow = currentFlexSum == 0;
      final isLastInRow = (currentFlexSum + flex) == FlexMapper.totalRowFlex;

      final leftPadding = isFirstInRow ? 0.0 : AppSpacing.xs;
      final rightPadding = isLastInRow ? 0.0 : AppSpacing.xs;

      currentRow.add(
        Expanded(
          flex: flex,
          child: Padding(
            padding: EdgeInsets.only(
              left: leftPadding,
              right: rightPadding,
              top: AppSpacing.xxs,
              bottom: AppSpacing.xxs,
            ),
            child: buildUiWidget(item),
          ),
        ),
      );

      currentFlexSum += flex;
    }

    // Add last row if it has remaining widgets
    if (currentRow.isNotEmpty) {
      rows.add(
        Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.sm),
          child: Row(children: currentRow),
        ),
      );
    }

    return Column(children: rows);
  }

  Widget buildUiWidget(widgetData) {
    List? temp = widgetData["enum_values"]?.map((e) => e.toString()).toList();
    final InputField field = InputField(
      inputId: widgetData["item_id"] ?? '',
      inputStackId: widgetData["input_stack_id"] != null
          ? int.tryParse(widgetData["input_stack_id"]!)
          : null,
      attributeId: widgetData["attribute_id"] ?? '',
      entityId: widgetData["entity_id"] ?? '',
      displayName: widgetData["display_name"] ??
          widgetData["attribute_name"] ??
          'Unknown Field',
      dataType: widgetData["data_type"] ?? 'String',
      sourceType: widgetData["source_type"] ?? 'user',
      required: widgetData["required"] ?? false,
      uiControl: widgetData["ui_control"] ?? 'oj-input-text',
      isVisible: widgetData["is_visible"] ?? true,
      readOnly: widgetData["read_only"] ??
          false, // Transfer readOnly property from Input
      allowedValues: temp
          ?.map(
            (e) => e.toString(),
          )
          .toList(),
      validations: null, // Convert if needed
      contextualId: widgetData["contextual_id"] ?? '',
      inputValue: widgetData["input_value"] ?? widgetData["default_value"],
      hasDropdownSource: widgetData["has_dropdown_source"] ?? false,
      dependencyType: widgetData["dependency_type"]?.toString(),
      metadata: InputFieldMetadata(
        usage: '',
        isInformational: widgetData["information_field"] ?? false,
        hasDropdownSource: widgetData["has_dropdown_source"] ?? false,
      ),
      dropdownOptions: null, // Convert if needed
      needsParentValue: widgetData["needs_parent_value"],
      parentIds: widgetData["parent_ids"]?.map((id) => id.toString()).toList(),
    );
    final fieldId = field.displayName;
    GlobalKey<InputFieldWidgetState> key;
    if (_inputFieldKeys.containsKey(fieldId)) {
      key = _inputFieldKeys[fieldId]!;
    } else {
      key = GlobalKey<InputFieldWidgetState>();
      _inputFieldKeys[fieldId] = key;
    }
    return utils_widget_factory.WidgetFactory.createInputWidget(
      context: context,
      field: field,
      key: key,
      sectionId: 'form',
      onValueChanged: (p0, p1, p2, p3) {
        setState(() {});
      },
    );
  }

  Widget _buildCompanyInfoFields(
      Map<String, dynamic> companyInfo, BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInputField(
                    "Company Name *",
                    companyInfo['company_name']?['placeholder'] ??
                        "Company name",
                    context,
                    controller: TextEditingController(
                        text: companyInfo['company_name']?['value'] ??
                            "TechCorp Inc."),
                    isEnriched:
                        companyInfo['company_name']?['auto_enriched'] == true,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    "✓ Company found & enriched",
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s10,
                      color: Colors.green,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyInter,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildField(
                "Industry",
                companyInfo['industry']?['value'] ?? "Software/SaaS",
                context,
                isReadonly: true,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildField(
                "Company Size",
                companyInfo['company_size']?['value'] ?? "500-1000 employees",
                context,
                isReadonly: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildField(
                    "Location",
                    companyInfo['company_location']?['value'] ??
                        "San Francisco, CA",
                    context,
                    isReadonly: true,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    "✓ In your territory",
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s10,
                      color: Colors.green,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyInter,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContactFields(
      Map<String, dynamic> contactInfo, BuildContext context) {
    // Extract job title options from JSON
    final jobTitleOptions = contactInfo['job_title']?['suggestions'] != null
        ? List<String>.from(contactInfo['job_title']['suggestions'])
        : <String>[];

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInputField(
                "Contact Name *",
                contactInfo['contact_name']?['placeholder'] ??
                    "Primary contact name",
                context,
                controller: _contactNameController,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInputField(
                "Email *",
                contactInfo['contact_email']?['placeholder'] ??
                    "<EMAIL>",
                context,
                controller: _contactEmailController,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDropdownField(
                "Job Title",
                _selectedJobTitle ??
                    (jobTitleOptions.isNotEmpty
                        ? jobTitleOptions.first
                        : "CTO"),
                context,
                options: jobTitleOptions.isNotEmpty
                    ? jobTitleOptions
                    : ["CTO", "VP Engineering", "Director IT", "CISO"],
                onChanged: (value) {
                  setState(() {
                    _selectedJobTitle = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInputField(
                "Phone",
                contactInfo['contact_phone']?['placeholder'] ??
                    "+****************",
                context,
                controller: _contactPhoneController,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOpportunityFields(
      Map<String, dynamic> opportunityInfo, BuildContext context) {
    // Extract options from JSON data
    final leadSourceOptions = opportunityInfo['lead_source']?['options'] != null
        ? List<String>.from(opportunityInfo['lead_source']['options'])
        : <String>[];

    final productInterestOptions =
        opportunityInfo['product_interest']?['options'] != null
            ? List<String>.from(opportunityInfo['product_interest']['options'])
            : <String>[];

    final urgencyLevelOptions =
        opportunityInfo['urgency_level']?['options'] != null
            ? List<String>.from(opportunityInfo['urgency_level']['options'])
            : <String>[];

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDropdownField(
                "Lead Source *",
                opportunityInfo['lead_source']?['selected_value'] ??
                    "Inbound Inquiry",
                context,
                options: leadSourceOptions,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDropdownField(
                "Product Interest *",
                opportunityInfo['product_interest']?['selected_value'] ??
                    "Enterprise Solution",
                context,
                options: productInterestOptions,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDropdownField(
                "Urgency Level",
                opportunityInfo['urgency_level']?['default'] ??
                    "Medium-term (3-6 months)",
                context,
                options: urgencyLevelOptions,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: Container()), // Empty space for layout
          ],
        ),
        const SizedBox(height: 12),
        _buildTextAreaField(
          "Inquiry Summary",
          opportunityInfo['inquiry_summary']?['placeholder'] ??
              "Brief summary of their inquiry or interest...",
          context,
        ),
      ],
    );
  }

  Widget _buildField(
    String label,
    String value,
    BuildContext context, {
    bool isReadonly = false,
    bool isPlaceholder = false,
    bool isEnriched = false,
    bool hasIndicator = false,
    String? indicatorText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontSize: MediaQuery.of(context).size.width > 1550
                    ? FontManager.s12
                    : FontManager.s10,
                fontWeight: FontManager.medium,
                color: Colors.black,
              ),
            ),
            if (isEnriched) ...[
              const SizedBox(width: 4),
              Text(
                "✓ Enriched",
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s10,
                  color: Colors.green,
                  fontWeight: FontManager.medium,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: isReadonly ? Colors.grey.shade50 : Colors.white,
          ),
          child: Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: MediaQuery.of(context).size.width > 1550
                  ? FontManager.s14
                  : FontManager.s12,
              fontFamily: FontManager.fontFamilyInter,
              color: isPlaceholder ? Colors.grey.shade500 : Colors.black,
            ),
          ),
        ),
        if (hasIndicator && indicatorText != null) ...[
          const SizedBox(height: 2),
          Text(
            indicatorText,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s10,
              color: Colors.green,
              fontWeight: FontManager.medium,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildInputField(
    String label,
    String placeholder,
    BuildContext context, {
    required TextEditingController controller,
    bool isEnriched = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontSize: MediaQuery.of(context).size.width > 1550
                ? FontManager.s12
                : FontManager.s10,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          style: FontManager.getCustomStyle(
            fontSize: MediaQuery.of(context).size.width > 1550
                ? FontManager.s14
                : FontManager.s12,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: FontManager.getCustomStyle(
              fontSize: MediaQuery.of(context).size.width > 1550
                  ? FontManager.s14
                  : FontManager.s12,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.grey.shade500,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: const Color(0xFF0058FF)),
            ),
          ),
          onChanged: (value) {
            print("$label changed to: $value");
          },
        ),
      ],
    );
  }

  Widget _buildDropdownField(String label, String value, BuildContext context,
      {List<String>? options, Function(String?)? onChanged}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontSize: MediaQuery.of(context).size.width > 1550
                ? FontManager.s12
                : FontManager.s10,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: options != null && options.isNotEmpty
              ? DropdownButtonFormField<String>(
                  value: options.contains(value) ? value : options.first,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 10),
                    border: InputBorder.none,
                  ),
                  style: FontManager.getCustomStyle(
                    fontSize: MediaQuery.of(context).size.width > 1550
                        ? FontManager.s14
                        : FontManager.s12,
                    fontFamily: FontManager.fontFamilyInter,
                    color: Colors.black,
                  ),
                  icon: Icon(Icons.keyboard_arrow_down,
                      size: 16, color: Colors.grey.shade600),
                  items: options.map((String option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(
                        option,
                        style: FontManager.getCustomStyle(
                          fontSize: MediaQuery.of(context).size.width > 1550
                              ? FontManager.s14
                              : FontManager.s12,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.black,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    // Handle dropdown value change
                    print("Dropdown changed to: $newValue");
                  },
                )
              : Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        value,
                        style: FontManager.getCustomStyle(
                          fontSize: MediaQuery.of(context).size.width > 1550
                              ? FontManager.s14
                              : FontManager.s12,
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                      Icon(Icons.keyboard_arrow_down,
                          size: 16, color: Colors.grey.shade600),
                    ],
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildTextAreaField(
      String label, String placeholder, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontSize: MediaQuery.of(context).size.width > 1550
                ? FontManager.s12
                : FontManager.s10,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: _inquirySummaryController,
          maxLines: 3,
          style: FontManager.getCustomStyle(
            fontSize: MediaQuery.of(context).size.width > 1550
                ? FontManager.s14
                : FontManager.s12,
            fontFamily: FontManager.fontFamilyInter,
            color: Colors.black,
          ),
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: FontManager.getCustomStyle(
              fontSize: MediaQuery.of(context).size.width > 1550
                  ? FontManager.s14
                  : FontManager.s12,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.grey.shade500,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: const Color(0xFF0058FF)),
            ),
          ),
          onChanged: (value) {
            print("$label changed to: $value");
          },
        ),
      ],
    );
  }

  Widget _buildLO2QualificationSections(
      Map<String, dynamic> detailedQualificationInputs, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Contact & Stakeholder Details Section
        if (detailedQualificationInputs['contact_stakeholder_details'] !=
            null) ...[
          Text(
            "Contact & Stakeholder Details",
            style: FontManager.getCustomStyle(
              fontSize: MediaQuery.of(context).size.width > 1550
                  ? FontManager.s14
                  : FontManager.s12,
              fontWeight: FontManager.medium,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          _buildContactStakeholderFields(
              detailedQualificationInputs['contact_stakeholder_details'],
              context),
          const SizedBox(height: 16),
        ],

        // BANT/MEDDIC Qualification Section
        if (detailedQualificationInputs['bant_meddic_qualification'] !=
            null) ...[
          Text(
            "Opportunity Qualification (BANT/MEDDIC)",
            style: FontManager.getCustomStyle(
              fontSize: MediaQuery.of(context).size.width > 1550
                  ? FontManager.s14
                  : FontManager.s12,
              fontWeight: FontManager.medium,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          _buildBANTMEDDICFields(
              detailedQualificationInputs['bant_meddic_qualification'],
              context),
          const SizedBox(height: 16),
        ],

        // Technical & Business Requirements Section
        if (detailedQualificationInputs['technical_business_requirements'] !=
            null) ...[
          Text(
            "Technical & Business Requirements",
            style: FontManager.getCustomStyle(
              fontSize: MediaQuery.of(context).size.width > 1550
                  ? FontManager.s14
                  : FontManager.s12,
              fontWeight: FontManager.medium,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          _buildTechnicalRequirementsFields(
              detailedQualificationInputs['technical_business_requirements'],
              context),
          const SizedBox(height: 16),
        ],

        // Next Steps Planning Section
        if (detailedQualificationInputs['next_steps_planning'] != null) ...[
          Text(
            "Next Steps & Follow-up Planning",
            style: FontManager.getCustomStyle(
              fontSize: MediaQuery.of(context).size.width > 1550
                  ? FontManager.s14
                  : FontManager.s12,
              fontWeight: FontManager.medium,
              fontFamily: FontManager.fontFamilyInter,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          _buildNextStepsFields(
              detailedQualificationInputs['next_steps_planning'], context),
        ],
      ],
    );
  }

  Widget _buildContactStakeholderFields(
      Map<String, dynamic> contactStakeholderData, BuildContext context) {
    final primaryContactDetails =
        contactStakeholderData['primary_contact_details'];
    final additionalStakeholders =
        contactStakeholderData['additional_stakeholders'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Primary Contact Details
        if (primaryContactDetails != null) ...[
          Row(
            children: [
              Expanded(
                child: _buildInputField(
                  "Contact Name *",
                  primaryContactDetails['contact_name']?['placeholder'] ??
                      "Primary contact name",
                  context,
                  controller: _contactNameController,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInputField(
                  "Contact Title",
                  primaryContactDetails['contact_title']?['placeholder'] ??
                      "Chief Technology Officer",
                  context,
                  controller: TextEditingController(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTextAreaField(
            "Pain Points",
            primaryContactDetails['contact_pain_points']?['placeholder'] ??
                "What specific challenges are they facing?",
            context,
          ),
        ],

        const SizedBox(height: 16),

        // Additional Stakeholders
        if (additionalStakeholders != null) ...[
          Text(
            "Additional Stakeholders",
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.medium,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  "Buying Committee Size",
                  "3-5 people",
                  context,
                  options: [
                    "1-2 people",
                    "3-5 people",
                    "6-10 people",
                    "10+ people",
                    "Unknown"
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Expanded(child: Container()),
            ],
          ),
          const SizedBox(height: 12),
          _buildTextAreaField(
            "Decision Process",
            additionalStakeholders['decision_process']?['placeholder'] ??
                "Describe their decision-making process...",
            context,
          ),
        ],
      ],
    );
  }

  Widget _buildBANTMEDDICFields(
      Map<String, dynamic> bantMeddicData, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Budget Qualification
        Text(
          "Budget Qualification",
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDropdownField(
                "Budget Status *",
                "Budget Identified",
                context,
                options: [
                  "Budget Approved",
                  "Budget Identified",
                  "Budget in Planning",
                  "No Budget Yet",
                  "Unknown"
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDropdownField(
                "Budget Range",
                "\$100K-\$200K",
                context,
                options: [
                  "<\$50K",
                  "\$50K-\$100K",
                  "\$100K-\$200K",
                  "\$200K-\$500K",
                  "\$500K+",
                  "Undisclosed"
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Need Qualification
        Text(
          "Need Qualification",
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        _buildTextAreaField(
          "Business Pain *",
          "What business problems are they trying to solve?",
          context,
        ),
        const SizedBox(height: 12),
        _buildTextAreaField(
          "Success Criteria",
          "How will they measure success?",
          context,
        ),

        const SizedBox(height: 16),

        // Timeline Qualification
        Text(
          "Timeline Qualification",
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDropdownField(
                "Decision Timeline *",
                "3-6 months",
                context,
                options: [
                  "< 1 month",
                  "1-3 months",
                  "3-6 months",
                  "6-12 months",
                  "> 12 months"
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDropdownField(
                "Implementation Timeline",
                "1-3 months",
                context,
                options: [
                  "< 1 month",
                  "1-3 months",
                  "3-6 months",
                  "6-12 months",
                  "> 12 months"
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTechnicalRequirementsFields(
      Map<String, dynamic> technicalRequirementsData, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextAreaField(
          "Primary Use Cases",
          "Primary use cases for our solution...",
          context,
        ),
        const SizedBox(height: 12),
        _buildTextAreaField(
          "Integration Needs",
          "Required integrations with existing systems...",
          context,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDropdownField(
                "User Count",
                "51-200 users",
                context,
                options: [
                  "1-10 users",
                  "11-50 users",
                  "51-200 users",
                  "201-1000 users",
                  "1000+ users"
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: Container()),
          ],
        ),
        const SizedBox(height: 12),
        _buildTextAreaField(
          "Decision Criteria",
          "How will they evaluate solutions?",
          context,
        ),
      ],
    );
  }

  Widget _buildNextStepsFields(
      Map<String, dynamic> nextStepsData, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDropdownField(
                "Next Meeting Type *",
                "Discovery Call",
                context,
                options: [
                  "Discovery Call",
                  "Demo/Presentation",
                  "Technical Deep Dive",
                  "Stakeholder Introduction",
                  "Proposal Review"
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDropdownField(
                "Meeting Timeline",
                "Next week",
                context,
                options: [
                  "This week",
                  "Next week",
                  "Within 2 weeks",
                  "Within month",
                  "TBD"
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildTextAreaField(
          "Information Needed",
          "What information do you need to gather?",
          context,
        ),
        const SizedBox(height: 12),
        _buildTextAreaField(
          "Qualification Summary",
          "Key qualification insights and next steps...",
          context,
        ),
      ],
    );
  }
}

class OptionComponent extends StatelessWidget {
  final Map<String, dynamic> data;

  const OptionComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    switch (data['type']) {
      case 'card':
        return GestureDetector(
          onTap: data['onTap'],
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 1),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  data['icon'],
                  width: MediaQuery.of(context).size.width > 1550 ? 16 : 14,
                  height: MediaQuery.of(context).size.width > 1550 ? 16 : 14,
                  colorFilter: const ColorFilter.mode(
                      Color(0xFF0058FF), BlendMode.srcIn),
                ),
                const SizedBox(width: 8),
                Text(
                  data['label'],
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width > 1550
                        ? FontManager.s14
                        : FontManager.s12,
                    color: Colors.black,
                    fontWeight: FontManager.regular,
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}

class RecommendationBox extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const RecommendationBox({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: data.map((item) {
        switch (item['type']) {
          case 'card':
            return Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300, width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Card title with icon
                  Row(
                    children: [
                      Text(
                        item['icon'] ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s16,
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          item['title'] ?? '',
                          style: FontManager.getCustomStyle(
                            fontSize: MediaQuery.of(context).size.width > 1550
                                ? FontManager.s14
                                : FontManager.s12,
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.medium,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Card content
                  if (item['content'] != null) ...[
                    ...((item['content'] as List<dynamic>).map((contentItem) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text(
                          contentItem.toString(),
                          style: FontManager.getCustomStyle(
                            fontSize: MediaQuery.of(context).size.width > 1550
                                ? FontManager.s13
                                : FontManager.s10,
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.regular,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      );
                    }).toList()),
                  ],

                  // Action link
                  if (item['action'] != null) ...[
                    const SizedBox(height: 8),
                    GestureDetector(
                      onTap: () {
                        print("${item['action']} clicked");
                      },
                      child: Text(
                        item['action'],
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontFamily: FontManager.fontFamilyInter,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFF0058FF),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          case 'text':
            return Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFFFF7DA),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  if (item['icon'] != null &&
                      item['icon'].toString().isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Text(
                        item['icon'],
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                    ),
                  Expanded(
                    child: Text(
                      item['value'],
                      style: FontManager.getCustomStyle(
                        fontSize: MediaQuery.of(context).size.width > 1550
                            ? FontManager.s14
                            : FontManager.s12,
                        fontFamily: FontManager.fontFamilyInter,
                        fontWeight: FontManager.medium,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ],
              ),
            );
          default:
            return const SizedBox.shrink();
        }
      }).toList(),
    );
  }
}

class ChatField extends StatefulWidget {
  final double? width;
  final double? height;
  final bool isGeneralLoading;
  final Function() onSendMessage;
  final Function()? onCancelRequest;
  final TextEditingController? controller;
  final List<Map<String, dynamic>>? actionButtonsData;

  ChatField({
    super.key,
    this.width,
    this.height,
    required this.isGeneralLoading,
    required this.onSendMessage,
    this.onCancelRequest,
    this.controller,
    this.actionButtonsData,
  });

  @override
  State<ChatField> createState() => _ChatFieldState();
}

class _ChatFieldState extends State<ChatField> {
  late TextEditingController chatController;
  final FocusNode _focusNode = FocusNode();
  bool _isShiftPressed = false;

  @override
  void initState() {
    super.initState();
    chatController = widget.controller ?? TextEditingController();
    chatController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      chatController.dispose();
    }
    chatController.removeListener(_onTextChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
  }

  bool get hasTextInChatField => chatController.text.trim().isNotEmpty;

  void _sendMessage() {
    if (widget.isGeneralLoading) {
      return;
    }

    final cleanedText = chatController.text.trim();
    if (cleanedText.isNotEmpty) {
      chatController.text = cleanedText;
      widget.onSendMessage();
    }
  }

  void _cancelRequest() {
    chatController.clear();
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
        provider.solutionWidgetsSelectedFrom ==
            ScreenConstants.myBusinessHome) {
      provider.currentScreenIndex = ScreenConstants.myBusinessHome;
      provider.solutionWidgetsSelectedFrom = "";
    } else if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
        provider.solutionWidgetsSelectedFrom ==
            ScreenConstants.myBusinessSolutions) {
      provider.currentScreenIndex = ScreenConstants.myBusinessSolutions;
      provider.solutionWidgetsSelectedFrom = "";
    }
    if (widget.onCancelRequest != null) {
      widget.onCancelRequest!();
    }
  }

  Future<void> _saveScreen() async {
    try {
      print("Save button pressed - saving screen data to local storage");

      final prefs = await SharedPreferences.getInstance();

      final screenData = {
        'currentText': chatController.text,
        'timestamp': DateTime.now().toIso8601String(),
        'screenType': 'web_solution_widgets',
      };

      final jsonString = jsonEncode(screenData);

      final key = 'screen_data_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(key, jsonString);
      await prefs.setString('latest_screen_data', jsonString);

      print("Data saved to local storage: $jsonString");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Screen data saved to local storage successfully!'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print("Error saving to local storage: $e");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save screen data!'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildButton({
    required Widget icon,
    required Function() onPressed,
    Color? iconColor,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        hoverColor: const Color(0xFF0058FF),
        child: Padding(
          padding: MediaQuery.of(context).size.width > 1550
              ? EdgeInsets.symmetric(
                  horizontal: AppSpacing.xs, vertical: AppSpacing.xxs)
              : EdgeInsets.all(AppSpacing.xs),
          child: icon,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        bottom: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      height: MediaQuery.of(context).size.width > 1550 ? 65 : 50,
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.xs, vertical: AppSpacing.xxs),
      child: Row(
        children: [
          Expanded(
            child: KeyboardListener(
              focusNode: _focusNode,
              onKeyEvent: (KeyEvent event) {
                if (event is KeyDownEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

                  if (event.logicalKey == LogicalKeyboardKey.enter) {
                    if (!_isShiftPressed && !widget.isGeneralLoading) {
                      _sendMessage();
                    }
                  }
                } else if (event is KeyUpEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
                }
              },
              child: TextField(
                cursorHeight: 16,
                controller: chatController,
                maxLines: 1,
                enabled: !widget.isGeneralLoading,
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: MediaQuery.of(context).size.width > 1550
                      ? FontManager.s16
                      : FontManager.s14,
                  fontWeight: FontManager.regular,
                ),
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm, vertical: AppSpacing.xs),
                  hintStyle: FontManager.getCustomStyle(
                    fontSize: MediaQuery.of(context).size.width > 1550
                        ? FontManager.s16
                        : FontManager.s14,
                    fontWeight: FontManager.regular,
                    color: Colors.grey.shade500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                  hintText: widget.isGeneralLoading
                      ? AppLocalizations.of(context)
                          .translate('home.sendingMessage')
                      : AppLocalizations.of(context).translate('home.askNSL'),
                  border: OutlineInputBorder(borderSide: BorderSide.none),
                  enabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  focusedBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
                  disabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                ),
                onSubmitted: (value) {
                  if (!widget.isGeneralLoading) {
                    _sendMessage();
                  }
                },
              ),
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Row(
            children: [
              // Generate buttons from JSON data
              if (widget.actionButtonsData != null)
                ...widget.actionButtonsData!.map((buttonData) {
                  return _buildButton(
                    icon: buttonData['image'] != null &&
                            buttonData['image'].toString().isNotEmpty
                        ? SvgPicture.asset(
                            buttonData['image'],
                            width: MediaQuery.of(context).size.width > 1550
                                ? 35
                                : 30,
                            height: MediaQuery.of(context).size.width > 1550
                                ? 35
                                : 30,
                          )
                        : Icon(
                            buttonData['text'] == 'Continue to Details →'
                                ? Icons.send
                                : buttonData['text'] == 'Cancel'
                                    ? Icons.close
                                    : Icons.save,
                            size: 20,
                          ),
                    onPressed: () {
                      if (buttonData['text'] == 'Continue to Details →') {
                        _sendMessage();
                      } else if (buttonData['text'] == 'Cancel') {
                        _cancelRequest();
                      } else if (buttonData['text'] == 'Save as Draft') {
                        _saveScreen();
                      }
                      print("${buttonData['text']} clicked");
                    },
                  );
                }).toList(),

              // Fallback buttons if no JSON data
              if (widget.actionButtonsData == null ||
                  widget.actionButtonsData!.isEmpty) ...[
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/cancel_soluton.svg',
                  ),
                  onPressed: _cancelRequest,
                ),
                const SizedBox(width: AppSpacing.xs),
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/save_sol.svg',
                  ),
                  onPressed: _saveScreen,
                ),
                const SizedBox(width: AppSpacing.xs),
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/send_sol.svg',
                  ),
                  onPressed: (hasTextInChatField && !widget.isGeneralLoading)
                      ? _sendMessage
                      : () {},
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}

class RightSectionWithTabs extends StatefulWidget {
  final Map<String, dynamic>? jsonData;
  final Map<String, dynamic>? solutionWidgetsData;
  bool isChatEnabled;
  bool isExpanded;
  Function? onExpansionChange;

  RightSectionWithTabs(
      {super.key,
      this.jsonData,
      this.solutionWidgetsData,
      this.isChatEnabled = false,
      this.isExpanded = false,
      this.onExpansionChange});

  @override
  State<RightSectionWithTabs> createState() => _RightSectionWithTabsState();
}

class _RightSectionWithTabsState extends State<RightSectionWithTabs>
    with TickerProviderStateMixin {
  int selectedIndex = 0;
  List<Map<String, dynamic>> tabs = [];
  Map<String, dynamic>? tabsData;
  bool hasRightSideData = false;

  // Store the current tab's data for reordering
  List<dynamic> currentTabData = [];

  // Animation controllers for right section transition
  late AnimationController _rightSlideAnimationController;
  late Animation<Offset> _rightSlideAnimation;
  late AnimationController _rightFadeAnimationController;
  late Animation<double> _rightFadeAnimation;

  // State management for right section
  bool _isRightTransitioning = false;
  bool _hasRightTransitioned = false;

  @override
  void initState() {
    super.initState();
    _initializeRightAnimations();
    _loadTabsData();
  }

  void _initializeRightAnimations() {
    // Slide animation for right section content
    _rightSlideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _rightSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -1.0),
    ).animate(CurvedAnimation(
      parent: _rightSlideAnimationController,
      curve: Curves.easeInOut,
    ));

    // Fade animation for smooth content transition
    _rightFadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _rightFadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _rightFadeAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _rightSlideAnimationController.dispose();
    _rightFadeAnimationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant RightSectionWithTabs oldWidget) {
    // Check if parent has transitioned and trigger right section transition
    final bool parentHasTransitioned = _checkTransitionState();
    if (parentHasTransitioned != _hasRightTransitioned &&
        !_isRightTransitioning) {
      _triggerRightTransition();
    }
    super.didUpdateWidget(oldWidget);
  }

  Future<void> _triggerRightTransition() async {
    if (_isRightTransitioning) return;

    setState(() {
      _isRightTransitioning = true;
    });

    // Start fade out animation
    await _rightFadeAnimationController.forward();

    // Start slide-up animation
    await _rightSlideAnimationController.forward();

    // Update state and reload tabs data
    setState(() {
      _hasRightTransitioned = true;
      _isRightTransitioning = false;
    });

    // Reload tabs data with new state
    await _loadTabsData();

    // Reset animations for next use
    _rightSlideAnimationController.reset();
    _rightFadeAnimationController.reset();
  }

  // Check if tab data is available
  bool _hasTabData() {
    return tabs.isNotEmpty;
  }

  // Get available tabs based on data availability
  List<Map<String, dynamic>> _getAvailableTabs() {
    return tabs
        .where((tab) => tab['data'] != null && (tab['data'] as List).isNotEmpty)
        .toList();
  }

  Future<void> _loadTabsData() async {
    try {
      // Determine which JSON file to load based on transition state
      // Check if we're in the transitioned state by looking at the parent widget's state
      final bool hasTransitioned = _checkTransitionState();

      final String jsonFile = hasTransitioned
          ? 'assets/data/solution_lo_one.json'
          : 'assets/data/solution_lo_two.json';

      final String dataKey = hasTransitioned
          ? 'lead_creation_lo2_information_hierarchy'
          : 'lead_creation_lo1_information_hierarchy';

      // Load the appropriate JSON data
      final String jsonString = await rootBundle.loadString(jsonFile);
      final Map<String, dynamic> data = jsonDecode(jsonString);

      // Extract the right section tabs data
      final rightSectionData = data[dataKey]?['extra_details'];

      if (rightSectionData != null &&
          rightSectionData['content_data'] != null) {
        final List<dynamic> tabsList = rightSectionData['content_data'];
        tabs = tabsList
            .map((tab) => {
                  "id": tab['id'],
                  "title": tab["title"],
                  "data": List.from(tab["data"]), // Store raw tab data
                })
            .toList();
        hasRightSideData = true;
      } else {
        // No tab data available
        tabs = [];
        hasRightSideData = false;
      }

      setState(() {});
    } catch (e) {
      print("Error loading tabs data: $e");
      // Set empty tabs on error
      tabs = [];
      setState(() {});
    }
  }

  // Helper method to check transition state from parent context
  bool _checkTransitionState() {
    // Try to access the parent WebSolutionWidgets state to check transition
    try {
      final context = this.context;
      final parentWidget =
          context.findAncestorWidgetOfExactType<WebSolutionWidgets>();
      if (parentWidget != null) {
        // Access the parent state through context
        final parentState =
            context.findAncestorStateOfType<_WebSolutionWidgetsState>();
        return parentState?._hasTransitioned ?? false;
      }
    } catch (e) {
      print("Could not access parent transition state: $e");
    }
    return false;
  }

  Widget buildTabData(List tabData, ValueChanged<List> onReorderComplete) {
    List<Map<String, dynamic>> currentTabData =
        List.from(tabData); // local copy

    return ReorderableListView.builder(
        buildDefaultDragHandles: false, // ⛔️ Disable default handle
        shrinkWrap: true,
        itemCount: currentTabData.length,
        onReorder: (int oldIndex, int newIndex) {
          if (newIndex > oldIndex) newIndex -= 1;

          final item = currentTabData.removeAt(oldIndex);
          currentTabData.insert(newIndex, item);

          print('Reordered: $oldIndex -> $newIndex');
          print(currentTabData.map((e) => e['type']).toList());

          // Notify parent of new order
          onReorderComplete(currentTabData);
        },
        itemBuilder: (context, index) {
          final e = currentTabData[index];
          final keyValue = 'tab_widget_${e['type']}_${e.hashCode}_$index';

          return _HoverableReorderItem(
            key: ValueKey(keyValue),
            index: index,
            child: buildTabWidget(e),
          );
        });
  }

  Widget buildTabWidget(Map<String, dynamic> tabWidget) {
    switch (tabWidget["type"]) {
      case "widget1":
        return widget1(tabWidget);
      case "widget2":
        return widget2(tabWidget);
      case "widget3":
        return widget3(tabWidget);
      case "widget4":
        return widget4(tabWidget);
      case "widget5":
        return widget5(tabWidget);
      case "widget6":
        return widget6(tabWidget);
      case "widget7":
        return widget7(tabWidget);
      case "widget8":
        return widget8(tabWidget);
      case "widget9":
        return widget9(tabWidget);
      case "widget10":
        return widget10(tabWidget);
      case "widget11":
        return widget11(tabWidget);
      case "widget12":
        return widget12(tabWidget);
      default:
        return Container();
    }
  }

  Widget widget1(Map<String, dynamic> tabWidgetData) {
    return companyProfileHeader(tabWidgetData);
  }

  Widget widget2(Map<String, dynamic> tabWidgetData) {
    final metrics = tabWidgetData["data"];
    return Column(children: [
      for (int i = 0; i < metrics.length; i += 2) ...[
        Row(
          children: [
            Expanded(
              child: _buildMetricCardFromJson(
                title: metrics[i]['title']["value"] ?? '',
                titleStyle: metrics[i]['title']["style"] ?? '',
                subtitle: metrics[i]['subtitle']["value"] ?? '',
                subtitleStyle: metrics[i]['subtitle']["style"] ?? '',
                tertiaryTitle: metrics[i]['tertiaryTitle']["value"] ?? '',
                tertiaryTitleStyle: metrics[i]['tertiaryTitle']["style"] ?? '',
              ),
            ),
            const SizedBox(width: 12),
            if (i + 1 < metrics.length)
              Expanded(
                child: _buildMetricCardFromJson(
                  title: metrics[i + 1]['title']["value"] ?? '',
                  titleStyle: metrics[i + 1]['title']["style"] ?? '',
                  subtitle: metrics[i + 1]['subtitle']["value"] ?? '',
                  subtitleStyle: metrics[i + 1]['subtitle']["style"] ?? '',
                  tertiaryTitle: metrics[i + 1]['tertiaryTitle']["value"] ?? '',
                  tertiaryTitleStyle:
                      metrics[i + 1]['tertiaryTitle']["style"] ?? '',
                ),
              )
            else
              Expanded(child: Container()),
          ],
        ),
        if (i + 2 < metrics.length) const SizedBox(height: 12),
      ],
    ]);
  }

  Widget widget3(Map<String, dynamic> tabWidgetData) {
    final techStack = tabWidgetData;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          techStack['title']['value'] ?? 'Technology Stack',
          style: _getTextStyleFromJson(techStack['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Wrap(
          spacing: AppSpacing.xs,
          runSpacing: AppSpacing.xs,
          children: (techStack['technologies']['data'] as List<dynamic>)
              .map((tech) => Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Text(
                      tech.toString(),
                      style: _getTextStyleFromJson(
                              techStack['technologies']['style']) ??
                          FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontManager.medium,
                            color: Colors.blue,
                          ),
                    ),
                  ))
              .toList(),
        ),
      ],
    );
  }

  Widget widget4(Map<String, dynamic> tabWidgetData) {
    final recentNews = tabWidgetData;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          recentNews['title']['value'] ?? 'Recent Company News',
          style: _getTextStyleFromJson(recentNews['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 10),
        ...(recentNews['subtitle'] as List<dynamic>).map((newsItem) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildNewsItemFromJson(newsItem),
              const SizedBox(height: 4),
              Divider(thickness: 0.5, color: Colors.grey.shade300),
              // const SizedBox(height: 4),
            ],
          );
        }).toList(),
      ],
    );
  }

  Widget widget5(Map<String, dynamic> tabWidgetData) {
    final industryContext = tabWidgetData;
    return _buildMarketIntelCard(
      title: industryContext['title']['value'] ?? '',
      titleStyle: _getTextStyleFromJson(industryContext['title']['style']),
      children: [
        // Metrics from JSON
        if (industryContext['subtitle'] != null) ...[
          Row(
            children: [
              Expanded(
                child: _buildMarketMetricItemFromJson(
                    industryContext['subtitle'][0]),
              ),
              const SizedBox(width: 12),
              if (industryContext['subtitle'].length > 1)
                Expanded(
                  child: _buildMarketMetricItemFromJson(
                      industryContext['subtitle'][1]),
                )
              else
                Expanded(child: Container()),
            ],
          ),
          const SizedBox(height: 12),
        ],

        //   // Key Trends
        if (industryContext['tertiaryTitle'] != null) ...[
          Text(
            industryContext['tertiaryTitle']['value'] ?? '',
            style: _getTextStyleFromJson(
                    industryContext['tertiaryTitle']['style']) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.semiBold,
                  color: Colors.black,
                ),
          ),
          const SizedBox(height: 6),
          ...(industryContext['tertiaryTitle']['data'] as List<dynamic>)
              .map((trend) {
            return _buildTrendItem(trend.toString(),
                industryContext['tertiaryTitle']['data_style']);
          }).toList(),
        ],
      ],
    );
  }

  Widget widget6(Map<String, dynamic> tabWidgetData) {
    final competitiveEnvironment = tabWidgetData;
    return _buildMarketIntelCard(
      title: competitiveEnvironment['title']['value'] ?? '',
      titleStyle:
          _getTextStyleFromJson(competitiveEnvironment['title']['style']),
      children: [
        //     // Competitors from JSON
        if (competitiveEnvironment['subtitle'] != null) ...[
          ...(competitiveEnvironment['subtitle'] as List<dynamic>)
              .map((competitor) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCompetitorItemFromJson(competitor),
                const SizedBox(height: 8),
                Text(
                  competitor['tertiaryTitle']['value'] ?? '',
                  style: _getTextStyleFromJson(
                          competitor['tertiaryTitle']['style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s10,
                        fontWeight: FontManager.regular,
                        color: Colors.grey.shade500,
                      ),
                ),
                const SizedBox(height: 12),
              ],
            );
          }).toList(),
        ],

        //     // Our Position
        if (competitiveEnvironment['tertiaryTitle'] != null) ...[
          Container(
            // padding: const EdgeInsets.only(left: 5,top: 10,bottom: 10,right: 5),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Text(
                  competitiveEnvironment['tertiaryTitle']['title']['value'] ??
                      '',
                  style: _getTextStyleFromJson(
                          competitiveEnvironment['tertiaryTitle']['title']
                              ['style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.regular,
                        color: Colors.black,
                      ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    competitiveEnvironment['tertiaryTitle']['subtitle']
                            ['value'] ??
                        '',
                    style: _getTextStyleFromJson(
                            competitiveEnvironment['tertiaryTitle']['subtitle']
                                ['style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.regular,
                          color: Colors.black,
                        ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
    // const SizedBox(height: 16),
  }

  Widget widget7(Map<String, dynamic> tabWidgetData) {
    final buyingSignals = tabWidgetData;
    return _buildMarketIntelCard(
      title: buyingSignals['title']['value'] ?? '',
      titleStyle: _getTextStyleFromJson(buyingSignals['title']['style']),
      children: [
        // Signals from JSON
        if (buyingSignals['subtitle'] != null) ...[
          Row(
            children: [
              Expanded(
                child: _buildSignalItemFromJson(buyingSignals['subtitle'][0]),
              ),
              const SizedBox(width: 12),
              if (buyingSignals['subtitle'].length > 1)
                Expanded(
                  child: _buildSignalItemFromJson(buyingSignals['subtitle'][1]),
                )
              else
                Expanded(child: Container()),
            ],
          ),
          const SizedBox(height: 16),
        ],

        // Intent Score
        if (buyingSignals['tertiaryTitle'] != null) ...[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                buyingSignals['tertiaryTitle']['title']['value'] ??
                    'Intent Score',
                style: _getTextStyleFromJson(
                        buyingSignals['tertiaryTitle']['title']['style']) ??
                    FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                buyingSignals['tertiaryTitle']['subtitle']['value'] ??
                    'Intent Score',
                style: _getTextStyleFromJson(
                        buyingSignals['tertiaryTitle']['subtitle']['style']) ??
                    FontManager.getCustomStyle(
                      fontSize: FontManager.s16,
                      fontWeight: FontManager.semiBold,
                      color: _parseColor(buyingSignals['tertiaryTitle']
                              ['subtitle']['style']['color']) ??
                          const Color(0xFF388E3C),
                    ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget widget8(Map<String, dynamic> tabWidgetData) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // West Coast Enterprise Header
          Container(
            padding: const EdgeInsets.all(12),
            // margin: EdgeInsets.only(top: AppSpacing.xs),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: const Color(0xFF0058FF),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            tabWidgetData['title']['value'] ?? '',
                            style: _getTextStyleFromJson(
                                    tabWidgetData['title']['style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s14,
                                  fontWeight: FontManager.semiBold,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                            maxLines: 2, // optional: limit to 2 lines
                            overflow: TextOverflow
                                .ellipsis, // optional: adds '...' on overflow
                            softWrap: true, // ensures it wraps to next line
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (tabWidgetData["hasExtraData"])
                      Container(
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFF3E0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              tabWidgetData['extra']['title']['value'] ?? '',
                              style: _getTextStyleFromJson(
                                      tabWidgetData['extra']['title']
                                          ['style']) ??
                                  FontManager.getCustomStyle(
                                    fontSize: FontManager.s14,
                                    fontWeight: FontManager.semiBold,
                                    color: const Color(0xFFE65100),
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              tabWidgetData['extra']['subtitle']['value'] ?? '',
                              style: _getTextStyleFromJson(
                                      tabWidgetData['extra']['subtitle']
                                          ['style']) ??
                                  FontManager.getCustomStyle(
                                    fontSize: FontManager.s12,
                                    fontWeight: FontManager.regular,
                                    color: Colors.grey.shade700,
                                  ),
                            ),
                            // const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    tabWidgetData['extra']['tertiaryTitle']
                                            ['value'] ??
                                        '',
                                    style: _getTextStyleFromJson(
                                            tabWidgetData['extra']
                                                ['tertiaryTitle']['style']) ??
                                        FontManager.getCustomStyle(
                                          fontSize: FontManager.s12,
                                          fontWeight: FontManager.medium,
                                          color: Colors.grey.shade700,
                                        ),
                                    maxLines: 2, // optional: limit to 2 lines
                                    overflow: TextOverflow
                                        .ellipsis, // optional: adds '...' on overflow
                                    softWrap:
                                        true, // ensures it wraps to next line
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            // Progress bar
                            Container(
                              height: 6,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor: 0.68,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF4CAF50),
                                    borderRadius: BorderRadius.circular(3),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 20),

                // Account Metrics Grid
                Row(
                  children: [
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][0]['title']['value'],
                          tabWidgetData['subtitle'][0]['subtitle']['value']),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][1]['title']['value'],
                          tabWidgetData['subtitle'][1]['subtitle']['value']),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][2]['title']['value'],
                          tabWidgetData['subtitle'][2]['subtitle']['value']),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildTerritoryMetricCard(
                          tabWidgetData['subtitle'][3]['title']['value'],
                          tabWidgetData['subtitle'][3]['subtitle']['value']),
                    ),
                  ],
                ),

                // const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget widget9(Map<String, dynamic> tabWidgetData) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with LEAD SCORING and confidence
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE91E63),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    tabWidgetData['title']['value'] ?? '',
                    style: _getTextStyleFromJson(
                            tabWidgetData['title']['style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontWeight: FontManager.semiBold,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                  ),
                ],
              ),
              Expanded(
                child: Text(
                  tabWidgetData['subtitle']['value'] ?? '',
                  style: _getTextStyleFromJson(
                          tabWidgetData['subtitle']['style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontManager.medium,
                        color: const Color(0xFF4CAF50),
                      ),
                  maxLines: 2, // optional: limit to 2 lines
                  overflow:
                      TextOverflow.ellipsis, // optional: adds '...' on overflow
                  softWrap: true, // ensures it wraps to next line
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Large score display
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      // Apply consistent height for both web and tablet
                      constraints: const BoxConstraints(
                        minHeight: 70, // Consistent height for both platforms
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            tabWidgetData['tertiaryTitle']['value'] ?? '',
                            style: _getTextStyleFromJson(
                                    tabWidgetData['tertiaryTitle']['style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: 28,
                                  fontWeight: FontManager.semiBold,
                                  color: _parseColor(
                                          tabWidgetData['tertiaryTitle']
                                              ['style']['color']) ??
                                      const Color(0xFF4CAF50),
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      // Apply consistent height for both web and tablet
                      constraints: const BoxConstraints(
                        minHeight: 70, // Consistent height for both platforms
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            tabWidgetData['extraData']['title']['value'] ?? '',
                            style: _getTextStyleFromJson(
                                    tabWidgetData['extraData']['title']
                                        ['style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s14,
                                  fontWeight: FontManager.semiBold,
                                  color: Colors.black,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            tabWidgetData['extraData']['subtitle']['value'] ??
                                '',
                            style: _getTextStyleFromJson(
                                    tabWidgetData['extraData']['subtitle']
                                        ['style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s10,
                                  fontWeight: FontManager.regular,
                                  color: Colors.blue,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget widget10(Map<String, dynamic> tabWidgetData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tabWidgetData['title']['value'] ?? '',
          style: _getTextStyleFromJson(tabWidgetData['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 12),
        ...tabWidgetData['subtitle']
            .map((detail) => _buildCompactScoreItem(
                  detail['title']['value'],
                  detail['subtitle']['value'],
                  detail['data'],
                ))
            .toList(),
      ],
    );
  }

  Widget widget11(Map<String, dynamic> tabWidgetData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tabWidgetData['title']['value'] ?? '',
          style: _getTextStyleFromJson(tabWidgetData['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 8),
        ...tabWidgetData['data']
            .map(
              (detail) => _buildImprovementItem(detail),
            )
            .toList()
      ],
    );
  }

  Widget widget12(Map<String, dynamic> tabWidgetData) {
    final dealSizing = tabWidgetData['deal_sizing_intelligence'] ?? {};
    // final salesCycle = tabWidgetData['sales_cycle_prediction'] ?? {};
    // final winProbability = tabWidgetData['win_probability'] ?? {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Deal Sizing Intelligence Section
        _buildOpportunityIntelSection(
          title: dealSizing['title'] ?? 'Deal Sizing Intelligence',
          titleStyle: _getTextStyleFromJson(dealSizing['style']),
          child: _buildDealSizingContent(dealSizing),
        ),

        const SizedBox(height: 20),
      ],
    );
  }

  // Build Stakeholders content from JSON data
  Widget _buildStakeholdersContentFromJson(
      Map<String, dynamic> content, Map<String, dynamic>? textStyle) {
    final predictedStakeholders = content['predicted_stakeholders'] ?? {};
    final organizationalInsights = content['organizational_insights'] ?? {};

    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Predicted Stakeholder Network Section
          _buildStakeholdersSection(
            title: predictedStakeholders['title'] ??
                'Predicted Stakeholder Network',
            titleStyle:
                _getTextStyleFromJson(predictedStakeholders['title_style']),
            child: _buildStakeholderNetworkContent(predictedStakeholders),
          ),

          const SizedBox(height: 20),

          // Organizational Context Section
          _buildStakeholdersSection(
            title: organizationalInsights['title'] ?? 'Organizational Context',
            titleStyle:
                _getTextStyleFromJson(organizationalInsights['title_style']),
            child: _buildOrganizationalContextContent(organizationalInsights),
          ),
        ],
      ),
    );
  }

  Widget _buildStakeholdersSection({
    required String title,
    TextStyle? titleStyle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            title,
            style: titleStyle ??
                FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.semiBold,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildStakeholderNetworkContent(
      Map<String, dynamic> stakeholdersData) {
    // Define the 4 stakeholders as shown in the UI
    final List<Map<String, dynamic>> stakeholders = [
      {
        'role': 'Chief Technology Officer',
        'influence': 'High',
        'concerns': ['Technical fit', 'Implementation complexity'],
        'strategy': 'Technical deep dive, reference calls',
      },
      {
        'role': 'VP Engineering',
        'influence': 'High',
        'concerns': ['Development impact', 'Tool efficiency'],
        'strategy': 'Developer experience demo',
      },
      {
        'role': 'CISO/Security Lead',
        'influence': 'Medium-High',
        'concerns': ['Security compliance', 'Data protection'],
        'strategy': 'Security docs, compliance overview',
      },
      {
        'role': 'CFO/Finance',
        'influence': 'High',
        'concerns': ['ROI', 'Cost justification'],
        'strategy': 'Business case, ROI analysis',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Stakeholder cards
        ...stakeholders.map((stakeholder) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Role title
                Text(
                  stakeholder['role'] ?? '',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.semiBold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),

                // Influence level with proper styling
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getInfluenceColor(stakeholder['influence'] ?? '')
                        .withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${stakeholder['influence'] ?? ''} influence',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: _getInfluenceColor(stakeholder['influence'] ?? ''),
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                // Concerns
                if (stakeholder['concerns'] != null) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Concerns: ',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Colors.black,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          (stakeholder['concerns'] as List<dynamic>).join(', '),
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontManager.regular,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],

                // Strategy
                if (stakeholder['strategy'] != null) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Strategy: ',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Colors.black,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          stakeholder['strategy'] ?? '',
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontWeight: FontManager.medium,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildOrganizationalContextContent(
      Map<String, dynamic> organizationalData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Company Structure and Decision Style in a row
        Row(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Company Structure',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.medium,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Engineering-driven',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontManager.medium,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Decision Style',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.medium,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Consensus-based',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontManager.medium,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Cultural Factors
        Text(
          'Cultural Factors:',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.semiBold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        // Cultural factors list
        ...[
          'Innovation-focused culture',
          'Data-driven decision making',
          'Strong technical team autonomy'
        ].map((factor) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    factor,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.regular,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Color _getInfluenceColor(String influence) {
    switch (influence.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium-high':
        return Colors.orange;
      case 'medium':
        return Colors.blue;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  // Build Competition content from JSON data
  Widget _buildCompetitionContentFromJson(
      Map<String, dynamic> content, Map<String, dynamic>? textStyle) {
    final competitiveLandscape = content['competitive_landscape'] ?? {};
    final differentiationStrategy = content['differentiation_strategy'] ?? {};

    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Competitive Analysis Section
          _buildCompetitionSection(
            title: competitiveLandscape['title'] ?? 'Competitive Analysis',
            titleStyle:
                _getTextStyleFromJson(competitiveLandscape['title_style']),
            child: _buildCompetitiveLandscapeContent(competitiveLandscape),
          ),

          const SizedBox(height: 20),

          // Key Differentiators Section
          _buildCompetitionSection(
            title: 'Key Differentiators',
            titleStyle: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              color: Colors.black,
            ),
            child: _buildKeyDifferentiatorsContent(),
          ),

          const SizedBox(height: 20),

          // Battle Plan Section
          _buildCompetitionSection(
            title: 'Battle Plan',
            titleStyle: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              color: Colors.black,
            ),
            child: _buildBattlePlanContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildCompetitionSection({
    required String title,
    TextStyle? titleStyle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Text(
            title,
            style: titleStyle ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.semiBold,
                  color: Colors.black,
                ),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildCompetitiveLandscapeContent(
      Map<String, dynamic> competitiveData) {
    final competitors = competitiveData['competitors'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Competitor cards
        ...competitors.map((competitor) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Competitor name and threat level
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      competitor['name'] ?? '',
                      style: _getTextStyleFromJson(competitor['name_style']) ??
                          FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            fontWeight: FontManager.semiBold,
                            color: Colors.black,
                          ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getThreatColor(competitor['threat'] ?? '')
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        competitor['threat'] ?? '',
                        style:
                            _getTextStyleFromJson(competitor['threat_style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s12,
                                  fontWeight: FontManager.medium,
                                  color: _getThreatColor(
                                      competitor['threat'] ?? ''),
                                ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Text(
                            competitor['position'] ?? '',
                            textAlign: TextAlign.center,
                            style: _getTextStyleFromJson(
                                    competitor['position_style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s12,
                                  fontWeight: FontManager.semiBold,
                                  color: Colors.grey.shade600,
                                ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Text(
                            competitor['activity'] ?? '',
                            textAlign: TextAlign.center,
                            style: _getTextStyleFromJson(
                                    competitor['activity_style']) ??
                                FontManager.getCustomStyle(
                                  fontSize: FontManager.s12,
                                  fontWeight: FontManager.regular,
                                  color: Colors.grey.shade600,
                                ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),

                // Our Advantages
                Text(
                  'Our Advantages:',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontManager.semiBold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 6),

                // Advantages list
                if (competitor['advantages'] != null) ...[
                  ...(competitor['advantages'] as List<dynamic>)
                      .map((advantage) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(top: 6),
                            width: 4,
                            height: 4,
                            decoration: BoxDecoration(
                              color: Colors.black,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              advantage.toString(),
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s12,
                                fontWeight: FontManager.regular,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildKeyDifferentiatorsContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Implementation Speed
        _buildDifferentiatorItem(
          'Implementation Speed',
          '2-3 weeks vs 2-3 months',
        ),
        const SizedBox(height: 12),

        // Integrations
        _buildDifferentiatorItem(
          'Integrations',
          '300+ out-of-box',
        ),
        const SizedBox(height: 12),

        // Customer Satisfaction
        _buildDifferentiatorItem(
          'Customer Satisfaction',
          '97% CSAT',
        ),
      ],
    );
  }

  Widget _buildDifferentiatorItem(String name, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          name,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            color: Colors.black,
          ),
        ),
        Text(
          value,
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.medium,
            color: Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildBattlePlanContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Key Messages
        Text(
          'Key Messages:',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.semiBold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        ...[
          'Emphasize rapid time-to-value',
          'Showcase integration ecosystem',
          'Highlight customer success stories',
          'Demonstrate scalability for growth'
        ].map((message) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    message,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.regular,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),

        const SizedBox(height: 16),

        // Trap Questions
        Text(
          'Trap Questions:',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s12,
            fontWeight: FontManager.semiBold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        ...[
          'Ask about implementation timeline concerns',
          'Discuss integration complexity challenges'
        ].map((question) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    question,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.regular,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Color _getThreatColor(String threat) {
    switch (threat.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low-medium':
        return Colors.green;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Widget companyProfileHeader(
    Map<String, dynamic> content,
  ) {
    // Company Header
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Expanded(
              child: _reusable(
            showTitle: false,
            subtitle: content['title']["value"] ?? '',
            subtitleStyle: content['title']["style"] ?? '',
            tertiaryTitle: content['subtitle']["value"] ?? '',
            tertiaryTitleStyle: content['subtitle']["style"] ?? '',
          )),
        ],
      ),
    );
  }

  Widget _buildMetricCardFromJson({
    bool showTitle = true,
    String? title,
    Map<String, dynamic>? titleStyle,
    String? subtitle,
    Map<String, dynamic>? subtitleStyle,
    String? tertiaryTitle,
    Map<String, dynamic>? tertiaryTitleStyle,
  }) {
    final colorMap = {
      'blue': Colors.blue,
      'green': Colors.green,
      'purple': Colors.purple,
      'orange': Colors.orange,
    };

    return Container(
        padding: const EdgeInsets.all(12),
        constraints: !kIsWeb ? const BoxConstraints(minHeight: 120) : null,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: _reusable(
            title: title ?? '',
            titleStyle: titleStyle,
            subtitle: subtitle,
            subtitleStyle: subtitleStyle,
            tertiaryTitle: tertiaryTitle,
            tertiaryTitleStyle: tertiaryTitleStyle));
  }

  Widget _reusable({
    bool showTitle = true,
    String? title,
    Map<String, dynamic>? titleStyle,
    String? subtitle,
    Map<String, dynamic>? subtitleStyle,
    String? tertiaryTitle,
    Map<String, dynamic>? tertiaryTitleStyle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment:
          !kIsWeb ? MainAxisAlignment.spaceBetween : MainAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            title ?? '',
            style: _getTextStyleFromJson(titleStyle) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.medium,
                  color: Colors.grey.shade600,
                ),
          ),
          const SizedBox(height: 6),
        ],
        Text(
          subtitle ?? '',
          style: _getTextStyleFromJson(subtitleStyle) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.semiBold,
                color: Colors.black,
              ),
        ),
        Text(
          tertiaryTitle ?? '',
          // metric['tertiaryTitle']['value'] ?? '',
          style: _getTextStyleFromJson(tertiaryTitleStyle
                  // metric['tertiaryTitle']['style']
                  ) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.regular,
                color: Colors.orange,
              ),
        ),
      ],
    );
  }

  Widget _buildNewsItemFromJson(Map<String, dynamic> newsItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          newsItem['title']['value'] ?? '',
          style: _getTextStyleFromJson(newsItem['title']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.black,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          newsItem['subtitle']['value'] ?? '',
          style: _getTextStyleFromJson(newsItem['subtitle']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          newsItem['tertiaryTitle']['value'] ?? '',
          style: _getTextStyleFromJson(newsItem['tertiaryTitle']['style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),
      ],
    );
  }

  // Build Opportunity Intel content from JSON data
  Widget _buildOpportunityIntelContentFromJson(
      Map<String, dynamic> content, Map<String, dynamic>? textStyle) {
    final dealSizing = content['deal_sizing_intelligence'] ?? {};
    final salesCycle = content['sales_cycle_prediction'] ?? {};
    final winProbability = content['win_probability'] ?? {};

    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Deal Sizing Intelligence Section
          _buildOpportunityIntelSection(
            title: dealSizing['title'] ?? 'Deal Sizing Intelligence',
            titleStyle: _getTextStyleFromJson(dealSizing['title_style']),
            child: _buildDealSizingContent(dealSizing),
          ),

          const SizedBox(height: 20),

          // Sales Cycle Prediction Section
          _buildOpportunityIntelSection(
            title: salesCycle['title'] ?? 'Sales Cycle Prediction',
            titleStyle: _getTextStyleFromJson(salesCycle['title_style']),
            child: _buildSalesCycleContent(salesCycle),
          ),

          const SizedBox(height: 20),

          // Win Probability Analysis Section
          _buildOpportunityIntelSection(
            title: winProbability['title'] ?? 'Win Probability Analysis',
            titleStyle: _getTextStyleFromJson(winProbability['title_style']),
            child: _buildWinProbabilityContent(winProbability),
          ),
        ],
      ),
    );
  }

  Widget _buildOpportunityIntelSection({
    required String title,
    TextStyle? titleStyle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(kIsWeb ? 16 : 12), // Reduced padding for tablets
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title and key value - responsive layout
          kIsWeb
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: titleStyle ??
                            FontManager.getCustomStyle(
                                fontSize: FontManager.s14,
                                fontWeight: FontManager.semiBold,
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyTiemposText),
                      ),
                    ),
                    // Display key values based on section type

                    Text(
                      '\$125,000',
                      style: FontManager.getCustomStyle(
                        fontSize: 20,
                        fontWeight: FontManager.semiBold,
                        color: Colors.blue,
                      ),
                    ),

                    Text(
                      '4.1 months',
                      style: FontManager.getCustomStyle(
                        fontSize: 20,
                        fontWeight: FontManager.semiBold,
                        color: Colors.blue,
                      ),
                    ),

                    Text(
                      '76%',
                      style: FontManager.getCustomStyle(
                        fontSize: 20,
                        fontWeight: FontManager.semiBold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title on top for tablets
                    Text(
                      title,
                      style: titleStyle ??
                          FontManager.getCustomStyle(
                              fontSize:
                                  FontManager.s12, // Smaller font for tablets
                              fontWeight: FontManager.semiBold,
                              color: Colors.black,
                              fontFamily: FontManager.fontFamilyTiemposText),
                    ),
                    const SizedBox(height: 8),
                    // Key value below title for tablets
                    if (title.contains('Deal Sizing Intelligence'))
                      Text(
                        '\$125,000',
                        style: FontManager.getCustomStyle(
                          fontSize: 16, // Smaller font for tablets
                          fontWeight: FontManager.semiBold,
                          color: Colors.blue,
                        ),
                      )
                    else if (title.contains('Sales Cycle Prediction'))
                      Text(
                        '4.1 months',
                        style: FontManager.getCustomStyle(
                          fontSize: 16, // Smaller font for tablets
                          fontWeight: FontManager.semiBold,
                          color: Colors.blue,
                        ),
                      )
                    else if (title.contains('Win Probability Analysis'))
                      Text(
                        '76%',
                        style: FontManager.getCustomStyle(
                          fontSize: 16, // Smaller font for tablets
                          fontWeight: FontManager.semiBold,
                          color: Colors.green,
                        ),
                      ),
                  ],
                ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  Widget _buildDealSizingContent(Map<String, dynamic> dealSizing) {
    final predictedDealSize = dealSizing['predicted_deal_size'] ?? {};
    final sizingFactors = dealSizing['sizing_factors'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Confidence and range information
        Text(
          '${predictedDealSize['confidence'] ?? '78% confidence'} \n${predictedDealSize['range'] ?? 'Range: \$85K - \$165K'}',
          style: _getTextStyleFromJson(predictedDealSize['confidence_style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),

        const SizedBox(height: 16),

        // Sizing factors in proper two-column layout
        Column(
          children: [
            // Company size factor row
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Company size factor',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'Enterprise tier',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            // const SizedBox(height: 8),

            // Product interest row
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Product interest',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'Full platform',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            // const SizedBox(height: 8),

            // Implementation row
            Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Implementation',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'Medium complexity',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s12,
                      fontWeight: FontManager.medium,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSalesCycleContent(Map<String, dynamic> salesCycle) {
    final predictedTimeline = salesCycle['predicted_timeline'] ?? {};
    final phaseBreakdown =
        salesCycle['phase_breakdown'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Confidence information
        Text(
          predictedTimeline['confidence'] ?? '84% confidence',
          style: _getTextStyleFromJson(predictedTimeline['confidence_style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),

        const SizedBox(height: 16),

        // Phase breakdown list
        ...phaseBreakdown.map((phase) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    phase['phase'] ?? '',
                    style: _getTextStyleFromJson(phase['phase_style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: Colors.black,
                        ),
                  ),
                ),
                Text(
                  phase['duration'] ?? '',
                  style: _getTextStyleFromJson(phase['duration_style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: Colors.grey.shade600,
                      ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildWinProbabilityContent(Map<String, dynamic> winProbability) {
    final currentProbability = winProbability['current_probability'] ?? {};
    final probabilityFactors =
        winProbability['probability_factors'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label information
        Text(
          currentProbability['label'] ?? 'Current probability',
          style: _getTextStyleFromJson(currentProbability['label_style']) ??
              FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.medium,
                color: Colors.grey.shade600,
              ),
        ),

        const SizedBox(height: 16),

        // Probability factors list
        ...probabilityFactors.map((factor) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    factor['factor'] ?? '',
                    style: _getTextStyleFromJson(factor['factor_style']) ??
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontManager.regular,
                          color: Colors.black,
                        ),
                  ),
                ),
                Text(
                  factor['impact'] ?? '',
                  style: _getTextStyleFromJson(factor['impact_style']) ??
                      FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: Colors.red,
                      ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildMarketIntelCard({
    required String title,
    required List<Widget> children,
    TextStyle? titleStyle,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                // or use Flexible(fit: FlexFit.tight)
                child: Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.semiBold,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  maxLines: 2, // optional: limit to 2 lines
                  overflow:
                      TextOverflow.ellipsis, // optional: adds '...' on overflow
                  softWrap: true, // ensures it wraps to next line
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTrendItem(String trend, style) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 2),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade600,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            trend,
            style: _getTextStyleFromJson(style) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  color: Colors.red,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildTerritoryMetricCard(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.medium,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactScoreItem(String title, String points, List details) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.medium,
                  color: Colors.black,
                ),
              ),
              Text(
                points,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.medium,
                  color: const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...details
              .map((detail) => Text(
                    detail,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s10,
                      fontWeight: FontManager.regular,
                      color: Colors.grey.shade600,
                    ),
                  ))
              .toList(),
          //   Divider(
          //   thickness: 1,
          //   color: Colors.grey.shade300,
          // ),
        ],
      ),
    );
  }

  Widget _buildImprovementItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.black,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to convert JSON text style to Flutter TextStyle
  TextStyle? _getTextStyleFromJson(Map<String, dynamic>? textStyleJson) {
    if (textStyleJson == null) return null;

    // Parse font size from JSON
    double fontSize = 12.0;
    if (textStyleJson['fontsize'] != null) {
      fontSize = (textStyleJson['fontsize'] as num).toDouble();
    }

    // Parse font weight from JSON
    FontWeight fontWeight = FontManager.medium;
    if (textStyleJson['fontweight'] != null) {
      final fontWeightStr = textStyleJson['fontweight'] as String;
      switch (fontWeightStr) {
        case 'w100':
          fontWeight = FontWeight.w100;
          break;
        case 'w200':
          fontWeight = FontWeight.w200;
          break;
        case 'w300':
          fontWeight = FontWeight.w300;
          break;
        case 'w400':
          fontWeight = FontWeight.w400;
          break;
        case 'w500':
          fontWeight = FontWeight.w500;
          break;
        case 'w600':
          fontWeight = FontWeight.w600;
          break;
        case 'w700':
          fontWeight = FontWeight.w700;
          break;
        case 'w800':
          fontWeight = FontWeight.w800;
          break;
        case 'w900':
          fontWeight = FontWeight.w900;
          break;
        default:
          fontWeight = FontManager.medium;
      }
    }

    // Parse color from JSON
    Color textColor = Colors.black;
    if (textStyleJson['color'] != null) {
      final colorStr = textStyleJson['color'] as String;
      switch (colorStr.toLowerCase()) {
        case 'blue':
          textColor = Colors.blue;
          break;
        case 'red':
          textColor = Colors.red;
          break;
        case 'green':
          textColor = Colors.green;
          break;
        case 'orange':
          textColor = Colors.orange;
          break;
        case 'purple':
          textColor = Colors.purple;
          break;
        case 'black':
          textColor = Colors.black;
          break;
        case 'white':
          textColor = Colors.white;
          break;
        case 'grey':
          textColor = Colors.grey.shade600;
          break;
        default:
          textColor = Colors.black;
      }
    }

    return FontManager.getCustomStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: textColor,
      fontFamily: FontManager.fontFamilyInter,
    );
  }

  // Helper methods for Market Intel JSON parsing
  Widget _buildMarketMetricItemFromJson(Map<String, dynamic> tabWidgetData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _reusable(
          showTitle: false,
          subtitle: tabWidgetData['title']["value"] ?? '',
          subtitleStyle: tabWidgetData['title']["style"] ?? '',
          tertiaryTitle: tabWidgetData['subtitle']["value"] ?? '',
          tertiaryTitleStyle: tabWidgetData['subtitle']["style"] ?? '',
        ),
      ],
    );
  }

  Widget _buildCompetitorItemFromJson(Map<String, dynamic> competitor) {
    return Row(
      children: [
        Expanded(
          child: Text(
            competitor['title']['value'] ?? '',
            style: _getTextStyleFromJson(competitor['title']['style']) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  color: Colors.black,
                ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color:
                _getThreatColor(competitor['subtitle']['style']['color'] ?? '')
                    .withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            competitor['subtitle']['value'] ?? '',
            style: _getTextStyleFromJson(competitor['subtitle']['style']) ??
                FontManager.getCustomStyle(
                  fontSize: FontManager.s10,
                  fontWeight: FontManager.medium,
                  color: _getThreatColor(
                      competitor['subtitle']['style']['color'] ?? ''),
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignalItemFromJson(Map<String, dynamic> buyingSignals) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _reusable(
            title: buyingSignals['title']["value"] ?? '',
            titleStyle: buyingSignals['title']["style"] ?? '',
            subtitle: buyingSignals['subtitle']["value"] ?? '',
            subtitleStyle: buyingSignals['subtitle']["style"] ?? '',
            tertiaryTitle: buyingSignals['tertiaryTitle']["value"] ?? '',
            tertiaryTitleStyle: buyingSignals['tertiaryTitle']["style"] ?? '',
          ),
        ],
      ),
    );
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null) return null;

    // Handle hex colors
    if (colorString.startsWith('#')) {
      try {
        return Color(
            int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      } catch (e) {
        return null;
      }
    }

    // Handle named colors
    switch (colorString.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      case 'black':
        return Colors.black;
      case 'white':
        return Colors.white;
      case 'grey':
        return Colors.grey;
      default:
        return null;
    }
  }

  // Helper method to get tab text style from JSON
  TextStyle _getTabTextStyle(
      Map<String, dynamic> tabData, bool isSelected, BuildContext context) {
    final textStyle = tabData['text_style'] as Map<String, dynamic>?;

    if (textStyle != null) {
      // Get the appropriate style based on selection state
      final styleToUse = isSelected
          ? (textStyle['selected'] as Map<String, dynamic>? ??
              textStyle['default'] as Map<String, dynamic>?)
          : textStyle['default'] as Map<String, dynamic>?;

      if (styleToUse != null) {
        // Parse font size from JSON
        double fontSize = 12.0;
        if (styleToUse['fontsize'] != null) {
          fontSize = (styleToUse['fontsize'] as num).toDouble();
        }

        // Parse font weight from JSON
        FontWeight fontWeight = FontManager.medium;
        if (styleToUse['fontweight'] != null) {
          final fontWeightStr = styleToUse['fontweight'] as String;
          switch (fontWeightStr) {
            case 'w100':
              fontWeight = FontWeight.w100;
              break;
            case 'w200':
              fontWeight = FontWeight.w200;
              break;
            case 'w300':
              fontWeight = FontWeight.w300;
              break;
            case 'w400':
              fontWeight = FontWeight.w400;
              break;
            case 'w500':
              fontWeight = FontWeight.w500;
              break;
            case 'w600':
              fontWeight = FontWeight.w600;
              break;
            case 'w700':
              fontWeight = FontWeight.w700;
              break;
            case 'w800':
              fontWeight = FontWeight.w800;
              break;
            case 'w900':
              fontWeight = FontWeight.w900;
              break;
            default:
              fontWeight = FontManager.medium;
          }
        }

        // Parse color from JSON
        Color textColor = Colors.black;
        if (styleToUse['color'] != null) {
          final colorStr = styleToUse['color'] as String;
          switch (colorStr.toLowerCase()) {
            case 'blue':
              textColor = Colors.blue;
              break;
            case 'red':
              textColor = Colors.red;
              break;
            case 'green':
              textColor = Colors.green;
              break;
            case 'orange':
              textColor = Colors.orange;
              break;
            case 'purple':
              textColor = Colors.purple;
              break;
            case 'black':
              textColor = Colors.black;
              break;
            case 'white':
              textColor = Colors.white;
              break;
            case 'grey':
              textColor = Colors.grey;
              break;
            default:
              textColor = Colors.black;
          }
        }

        return FontManager.getCustomStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: textColor,
          fontFamily: FontManager.fontFamilyInter,
        );
      }
    }

    // Fallback to default styling if no text_style in JSON
    return FontManager.getCustomStyle(
      fontSize: MediaQuery.of(context).size.width > 1550
          ? FontManager.s14
          : FontManager.s12,
      fontWeight: isSelected ? FontManager.semiBold : FontManager.medium,
      color: Colors.black,
      fontFamily: FontManager.fontFamilyInter,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: tabs.isEmpty
          ? Container()
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(color: Colors.grey.shade300),
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    children: List.generate(tabs.length, (index) {
                      final isSelected = selectedIndex == index;

                      return Expanded(
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => setState(() => selectedIndex = index),
                            child: Container(
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? const Color(0xFFF1F5FB)
                                    : Colors.white,
                                // border: Border.all(color: Colors.grey.shade300)
                              ),
                              alignment: Alignment.center,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Text(
                                tabs[index]["title"] ?? '',
                                style: _getTabTextStyle(
                                    tabs[index], isSelected, context),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                  ),
                ),

                // Tab content with animations
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: AppSpacing.lg, horizontal: AppSpacing.md),
                    width: double.infinity,
                    child: SlideTransition(
                      position: _rightSlideAnimation,
                      child: FadeTransition(
                        opacity: _rightFadeAnimation,
                        child: selectedIndex <= tabs.length - 1 &&
                                tabs[selectedIndex].containsKey("data")
                            ? buildTabData(
                                tabs[selectedIndex]["data"],
                                (List newOrder) {
                                  setState(() {
                                    tabs[selectedIndex]["data"] = newOrder;
                                  });
                                },
                              )
                            : Text(
                                tabs[selectedIndex]["content"] as String,
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      MediaQuery.of(context).size.width > 1550
                                          ? FontManager.s16
                                          : FontManager.s14,
                                  fontFamily: FontManager.fontFamilyInter,
                                ),
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}

// A button with hover effect that toggles expansion
class _HoverExpandButton extends StatefulWidget {
  final bool isExpanded;
  final VoidCallback onTap;
  final String tooltipMessage;

  const _HoverExpandButton({
    required this.isExpanded,
    required this.onTap,
    this.tooltipMessage = '',
    Key? key,
  }) : super(key: key);

  @override
  State<_HoverExpandButton> createState() => _HoverExpandButtonState();
}

class _HoverExpandButtonState extends State<_HoverExpandButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: Tooltip(
        message: widget.tooltipMessage,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isHovered ? const Color(0xffE4EDFF) : Colors.transparent,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: widget.isExpanded
                  ? Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..scale(-1.0, 1.0, 1.0), // Flip icon horizontally
                      child: Icon(
                        Icons.login,
                        size: 16,
                        color: isHovered
                            ? const Color(0xff0058FF)
                            : Colors.grey.shade600,
                      ),
                    )
                  : Icon(
                      Icons.chevron_right,
                      size: 16,
                      color: isHovered
                          ? const Color(0xff0058FF)
                          : Colors.grey.shade600,
                    ),
            ),
          ),
        ),
      ),
    );
  }
}

class _HoverableReorderItem extends StatefulWidget {
  final Widget child;
  final int index;

  const _HoverableReorderItem({
    Key? key,
    required this.child,
    required this.index,
  }) : super(key: key);

  @override
  State<_HoverableReorderItem> createState() => _HoverableReorderItemState();
}

class _HoverableReorderItemState extends State<_HoverableReorderItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: AppSpacing.lg),
            child: widget.child,
          ),
          if (isHovered)
            Positioned(
              top: 8,
              right: 8,
              child: ReorderableDragStartListener(
                index: widget.index,
                child: CustomImage.asset(
                  "assets/images/my_business/expand_collection.svg",
                  width: 18,
                  height: 18,
                ).toWidget(),
              ),
            ),
        ],
      ),
    );
  }
}
