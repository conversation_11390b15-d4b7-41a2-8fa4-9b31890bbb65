import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/widgets/chat_action_icon.dart';
import 'package:nsl/widgets/audio_player_controls.dart';
import 'package:audioplayers/audioplayers.dart';

/// A reusable widget for message action buttons that displays copy, thumbs up, thumbs down, and text-to-speech buttons.
class MessageActionButtons extends StatelessWidget {
  /// The message content.
  final String messageContent;

  /// The message ID.
  final String messageId;

  /// The audio player instance.
  final AudioPlayer audioPlayer;

  /// The current playing message ID.
  final String? currentPlayingMessageId;

  /// Whether audio is currently playing.
  final bool isPlaying;

  /// Whether audio is currently paused.
  final bool isPaused;

  /// The current position in the audio.
  final Duration currentPosition;

  /// The total duration of the audio.
  final Duration totalDuration;

  /// Callback when the text-to-speech button is tapped.
  final Function(String, {String? messageId}) onTextToSpeech;

  /// Callback when the stop button is tapped.
  final VoidCallback onStopAudio;

  /// Callback to show the copy overlay.
  final Function(BuildContext) showCopyOverlay;

  /// Processing time from API response.
  final double? processingTime;

  /// Total tokens used from API response.
  final int? totalTokens;

  const MessageActionButtons({
    super.key,
    required this.messageContent,
    required this.messageId,
    required this.audioPlayer,
    required this.currentPlayingMessageId,
    required this.isPlaying,
    required this.isPaused,
    required this.currentPosition,
    required this.totalDuration,
    required this.onTextToSpeech,
    required this.onStopAudio,
    required this.showCopyOverlay,
    this.processingTime,
    this.totalTokens,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
     mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
         // Add spacing before processing info
        if (processingTime != null || totalTokens != null) ...[
          // SizedBox(width: AppSpacing.xs),

          // Processing time and tokens info
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (processingTime != null) ...[
                      Icon(
                        Icons.timer_outlined,
                        size: 12,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4),
                      Text(
                        '${processingTime!.toStringAsFixed(2)}s',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                    if (processingTime != null && totalTokens != null)
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 6),
                        width: 1,
                        height: 12,
                        color: Colors.grey[400],
                      ),
                    if (totalTokens != null) ...[
                      Icon(
                        Icons.token_outlined,
                        size: 12,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4),
                      Text(
                        '$totalTokens tokens',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        
        // Copy icon
        Row(
          children: [
            ChatActionIcon(
              iconPath: 'assets/images/chat/copy.svg',
              onTap: () {
                // Handle copy action
                Clipboard.setData(ClipboardData(text: messageContent));
                // Log the action
                Logger.info('Message copied to clipboard');
                // Show a small overlay notification
                showCopyOverlay(context);
              },
              tooltip: 'Copy message',
            ),
          
        SizedBox(width: AppSpacing.xxs / 2),

        // Thumbs up icon
        ChatActionIcon(
          iconPath: 'assets/images/chat/thumbs_up.svg',
          onTap: () {
            // Handle thumbs up action
            Logger.info('Thumbs up clicked');
          },
          tooltip: 'Thumbs up',
        ),
        SizedBox(width: AppSpacing.xxs / 2),

        // Thumbs down icon
        ChatActionIcon(
          iconPath: 'assets/images/chat/thumbs_down.svg',
          onTap: () {
            // Handle thumbs down action
            Logger.info('Thumbs down clicked');
          },
          tooltip: 'Thumbs down',
        ),
        SizedBox(width: AppSpacing.xxs / 2),

        // Voice icon for text-to-speech
        currentPlayingMessageId == messageId && isPlaying
            ? AudioPlayerControls(
                audioPlayer: audioPlayer,
                currentPosition: currentPosition,
                totalDuration: totalDuration,
                isPaused: isPaused,
                onPlayPause: () {
                  Logger.info('Play/Pause button pressed. isPaused: $isPaused');
                  try {
                    if (isPaused) {
                      Logger.info('Resuming audio playback');
                      audioPlayer.resume();
                    } else {
                      Logger.info('Pausing audio playback');
                      audioPlayer.pause();
                    }
                  } catch (e) {
                    Logger.error('Error toggling play/pause: $e');
                  }
                },
                onStop: onStopAudio,
                onSeek: (position) {
                  audioPlayer.seek(position);
                },
              )
            : ChatActionIcon(
                iconPath: 'assets/images/chat/volume_speaker.svg',
                onTap: () {
                  onTextToSpeech(messageContent, messageId: messageId);
                },
                tooltip: 'Listen to this message',
              ),

       ],
        ),
        ],
      ],
    );
  }
}
