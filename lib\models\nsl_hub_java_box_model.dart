import 'dart:convert';

NslHubJavaBoxModel nslHubJavaBoxModelFromJson(String str) => 
    NslHubJavaBoxModel.fromJson(json.decode(str));

String nslHubJavaBoxModelToJson(NslHubJavaBoxModel data) => 
    json.encode(data.toJson());

class NslHubJavaBoxModel {
  bool? success;
  String? message;
  List<NslHubJavaBoxData>? data;
  int? totalGroups;
  int? totalItems;

  NslHubJavaBoxModel({
    this.success,
    this.message,
    this.data,
    this.totalGroups,
    this.totalItems,
  });

  NslHubJavaBoxModel copyWith({
    bool? success,
    String? message,
    List<NslHubJavaBoxData>? data,
    int? totalGroups,
    int? totalItems,
  }) =>
      NslHubJavaBoxModel(
        success: success ?? this.success,
        message: message ?? this.message,
        data: data ?? this.data,
        totalGroups: totalGroups ?? this.totalGroups,
        totalItems: totalItems ?? this.totalItems,
      );

  factory NslHubJavaBoxModel.fromJson(Map<String, dynamic> json) => 
      NslHubJavaBoxModel(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null 
            ? [] 
            : List<NslHubJavaBoxData>.from(
                json["data"]!.map((x) => NslHubJavaBoxData.fromJson(x))
              ),
        totalGroups: json["totalGroups"],
        totalItems: json["totalItems"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": data == null 
            ? [] 
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "totalGroups": totalGroups,
        "totalItems": totalItems,
      };
}

class NslHubJavaBoxData {
  int? id;
  String? nslName;
  String? naturalLanguage;
  String? nslGroup;
  String? startLines;
  String? endLines;
  String? type;
  String? goId;
  String? createTime;
  String? nslStartLines;
  String? nslEndLines;
  int? itemCount;
  String? javaVarName;

  NslHubJavaBoxData({
    this.id,
    this.nslName,
    this.naturalLanguage,
    this.nslGroup,
    this.startLines,
    this.endLines,
    this.type,
    this.goId,
    this.createTime,
    this.nslStartLines,
    this.nslEndLines,
    this.itemCount,
    this.javaVarName,
  });

  NslHubJavaBoxData copyWith({
    int? id,
    String? nslName,
    String? naturalLanguage,
    String? nslGroup,
    String? startLines,
    String? endLines,
    String? type,
    String? goId,
    String? createTime,
    String? nslStartLines,
    String? nslEndLines,
    int? itemCount,
    String? javaVarName,
  }) =>
      NslHubJavaBoxData(
        id: id ?? this.id,
        nslName: nslName ?? this.nslName,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        nslGroup: nslGroup ?? this.nslGroup,
        startLines: startLines ?? this.startLines,
        endLines: endLines ?? this.endLines,
        type: type ?? this.type,
        goId: goId ?? this.goId,
        createTime: createTime ?? this.createTime,
        nslStartLines: nslStartLines ?? this.nslStartLines,
        nslEndLines: nslEndLines ?? this.nslEndLines,
        itemCount: itemCount ?? this.itemCount,
        javaVarName: javaVarName ?? this.javaVarName,
      );

  factory NslHubJavaBoxData.fromJson(Map<String, dynamic> json) => 
      NslHubJavaBoxData(
        id: json["id"],
        nslName: json["nslName"],
        naturalLanguage: json["naturalLanguage"],
        nslGroup: json["nslGroup"],
        startLines: json["startLines"],
        endLines: json["endLines"],
        type: json["type"],
        goId: json["goId"],
        createTime: json["createTime"],
        nslStartLines: json["nslStartLines"],
        nslEndLines: json["nslEndLines"],
        itemCount: json["itemCount"],
        javaVarName: json["javaVarName"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "nslName": nslName,
        "naturalLanguage": naturalLanguage,
        "nslGroup": nslGroup,
        "startLines": startLines,
        "endLines": endLines,
        "type": type,
        "goId": goId,
        "createTime": createTime,
        "nslStartLines": nslStartLines,
        "nslEndLines": nslEndLines,
        "itemCount": itemCount,
        "javaVarName": javaVarName,
      };
}
