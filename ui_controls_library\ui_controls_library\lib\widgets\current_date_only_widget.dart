import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:convert';
import 'package:ui_controls_library/utils/callback_interpreter.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

class CurrentDateOnlyWidget extends StatefulWidget {
  // Configurable properties
  final String format;
  final bool showWeekday;
  final bool showYear;
  final bool showMonth;
  final bool showDay;
  final bool isLiveUpdate;
  final int updateIntervalSeconds;
  final String locale;
  final TextStyle? textStyle;
  final Color textColor;
  final Color backgroundColor;
  final double fontSize;
  final FontWeight fontWeight;
  final TextAlign textAlign;
  final bool hasBorder;
  final double borderRadius;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;
  final bool isCompact;
  final String? label;
  final String? prefix;
  final String? suffix;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final bool isDarkTheme;
  final bool showIcon;
  final IconData? icon;
  final bool isAnimated;
  final String? customText;
  final bool isUpperCase;
  final bool isLowerCase;
  final bool isCapitalized;
  final bool isItalic;
  final bool isBold;
  final bool isUnderlined;
  final double padding;
  final double width;
  final double height;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final Function(DateTime)? onDateSelected;
  final DateTime? testDate; // For testing purposes only

  // Advanced interaction properties
  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  // JSON callback properties
  /// Dynamic callback definitions from JSON
  ///
  /// This can include callback definitions for various events:
  /// - onDateSelected: Executed when a date is selected
  /// - onTap: Executed when the widget is tapped
  /// - onDoubleTap: Executed when the widget is double-tapped
  /// - onLongPress: Executed when the widget is long-pressed
  /// - onHover: Executed when the mouse enters or exits the widget
  /// - onFocus: Executed when the widget gains or loses focus
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use dynamic callbacks from JSON
  final bool useJsonCallbacks;

  /// State map for dynamic callbacks
  ///
  /// This map can be used by dynamic callbacks to store and retrieve state.
  final Map<String, dynamic>? callbackState;

  /// Custom handlers for dynamic callbacks
  ///
  /// This map contains custom handler functions that can be called by dynamic callbacks.
  final Map<String, Function>? customCallbackHandlers;

  // Advanced date formatting options
  /// Custom date formatter function
  ///
  /// This function allows for custom formatting of the date beyond what's possible
  /// with the standard format string.
  final String Function(DateTime)? customDateFormatter;

  /// Whether to use a relative date format (e.g., "Today", "Yesterday")
  final bool useRelativeDates;

  /// Map of special date formats for specific dates
  ///
  /// This map allows for special formatting of specific dates.
  /// The keys are date strings in the format "yyyy-MM-dd", and the values are
  /// the display strings for those dates.
  final Map<String, String>? specialDateFormats;

  const CurrentDateOnlyWidget({
    super.key,
    this.format = '',
    this.showWeekday = false,
    this.showYear = true,
    this.showMonth = true,
    this.showDay = true,
    this.isLiveUpdate = false,
    this.updateIntervalSeconds = 1,
    this.locale = 'en_US',
    this.textStyle,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.textAlign = TextAlign.center,
    this.hasBorder = true,
    this.borderRadius = 4.0,
    this.borderColor = const Color(0xFFE0E0E0),
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isCompact = false,
    this.label,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.isDarkTheme = false,
    this.showIcon = false,
    this.icon,
    this.isAnimated = false,
    this.customText,
    this.isUpperCase = false,
    this.isLowerCase = false,
    this.isCapitalized = false,
    this.isItalic = false,
    this.isBold = false,
    this.isUnderlined = false,
    this.padding = 16.0,
    this.width = double.infinity,
    this.height = 0,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.onDateSelected,
    this.testDate,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    this.onTap,
    this.focusNode,
    this.autofocus = false,
    // JSON callback properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    // Advanced date formatting options
    this.customDateFormatter,
    this.useRelativeDates = false,
    this.specialDateFormats,
  });

  /// Creates a CurrentDateOnlyWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the CurrentDateOnlyWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "format": "yyyy-MM-dd",
  ///   "showWeekday": true,
  ///   "textColor": "blue",
  ///   "hasBorder": true,
  ///   "label": "Current Date"
  /// }
  /// ```
  factory CurrentDateOnlyWidget.fromJson(Map<String, dynamic> json) {
    // Handle text alignment
    TextAlign textAlign = TextAlign.center;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'end':
        case 'right':
          textAlign = TextAlign.end;
          break;
        case 'start':
        case 'left':
          textAlign = TextAlign.start;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
      }
    }

    // Handle font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      } else if (json['fontWeight'] is int) {
        final weight = json['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    // Handle main axis alignment
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.center;
    if (json['mainAxisAlignment'] != null) {
      switch (json['mainAxisAlignment'].toString().toLowerCase()) {
        case 'start':
          mainAxisAlignment = MainAxisAlignment.start;
          break;
        case 'end':
          mainAxisAlignment = MainAxisAlignment.end;
          break;
        case 'center':
          mainAxisAlignment = MainAxisAlignment.center;
          break;
        case 'spaceBetween':
        case 'space_between':
        case 'space between':
          mainAxisAlignment = MainAxisAlignment.spaceBetween;
          break;
        case 'spaceAround':
        case 'space_around':
        case 'space around':
          mainAxisAlignment = MainAxisAlignment.spaceAround;
          break;
        case 'spaceEvenly':
        case 'space_evenly':
        case 'space evenly':
          mainAxisAlignment = MainAxisAlignment.spaceEvenly;
          break;
      }
    }

    // Handle cross axis alignment
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center;
    if (json['crossAxisAlignment'] != null) {
      switch (json['crossAxisAlignment'].toString().toLowerCase()) {
        case 'start':
          crossAxisAlignment = CrossAxisAlignment.start;
          break;
        case 'end':
          crossAxisAlignment = CrossAxisAlignment.end;
          break;
        case 'center':
          crossAxisAlignment = CrossAxisAlignment.center;
          break;
        case 'stretch':
          crossAxisAlignment = CrossAxisAlignment.stretch;
          break;
        case 'baseline':
          crossAxisAlignment = CrossAxisAlignment.baseline;
          break;
      }
    }

    // Handle icon data
    IconData? prefixIcon;
    if (json['prefixIcon'] != null) {
      prefixIcon = _getIconData(json['prefixIcon'].toString());
    }

    IconData? suffixIcon;
    if (json['suffixIcon'] != null) {
      suffixIcon = _getIconData(json['suffixIcon'].toString());
    }

    IconData? icon;
    if (json['icon'] != null) {
      icon = _getIconData(json['icon'].toString());
    }

    // Handle test date for testing purposes
    DateTime? testDate;
    if (json['testDate'] != null) {
      try {
        if (json['testDate'] is String) {
          testDate = DateTime.parse(json['testDate']);
        } else if (json['testDate'] is Map) {
          final Map<String, dynamic> dateMap = json['testDate'];
          final int year = dateMap['year'] ?? DateTime.now().year;
          final int month = dateMap['month'] ?? 1;
          final int day = dateMap['day'] ?? 1;
          final int hour = dateMap['hour'] ?? 0;
          final int minute = dateMap['minute'] ?? 0;
          final int second = dateMap['second'] ?? 0;
          testDate = DateTime(year, month, day, hour, minute, second);
        }
      } catch (e) {
        // If parsing fails, leave testDate as null
      }
    }

    // Parse advanced interaction properties
    Color? hoverColor;
    if (json['hoverColor'] != null) {
      hoverColor = _colorFromJson(json['hoverColor']);
    }

    Color? focusColor;
    if (json['focusColor'] != null) {
      focusColor = _colorFromJson(json['focusColor']);
    }

    String? tooltip = json['tooltip'] as String?;
    bool enableFeedback = json['enableFeedback'] as bool? ?? true;
    bool autofocus = json['autofocus'] as bool? ?? false;

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse advanced date formatting options
    bool useRelativeDates = json['useRelativeDates'] as bool? ?? false;

    Map<String, String>? specialDateFormats;
    if (json['specialDateFormats'] != null &&
        json['specialDateFormats'] is Map) {
      specialDateFormats = Map<String, String>.from(
        (json['specialDateFormats'] as Map).map(
          (key, value) => MapEntry(key.toString(), value.toString()),
        ),
      );
    }

    return CurrentDateOnlyWidget(
      format: json['format'] as String? ?? '',
      showWeekday: json['showWeekday'] as bool? ?? false,
      showYear: json['showYear'] as bool? ?? true,
      showMonth: json['showMonth'] as bool? ?? true,
      showDay: json['showDay'] as bool? ?? true,
      isLiveUpdate: json['isLiveUpdate'] as bool? ?? false,
      updateIntervalSeconds: json['updateIntervalSeconds'] as int? ?? 1,
      locale: json['locale'] as String? ?? 'en_US',
      textColor: _colorFromJson(json['textColor']) ?? Colors.black,
      backgroundColor: _colorFromJson(json['backgroundColor']) ?? Colors.white,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      textAlign: textAlign,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderColor:
          _colorFromJson(json['borderColor']) ?? const Color(0xFFCCCCCC),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isCompact: json['isCompact'] as bool? ?? false,
      label: json['label'] as String?,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      showIcon: json['showIcon'] as bool? ?? false,
      icon: icon,
      isAnimated: json['isAnimated'] as bool? ?? false,
      customText: json['customText'] as String?,
      isUpperCase: json['isUpperCase'] as bool? ?? false,
      isLowerCase: json['isLowerCase'] as bool? ?? false,
      isCapitalized: json['isCapitalized'] as bool? ?? false,
      isItalic: json['isItalic'] as bool? ?? false,
      isBold: json['isBold'] as bool? ?? false,
      isUnderlined: json['isUnderlined'] as bool? ?? false,
      padding: (json['padding'] as num?)?.toDouble() ?? 16.0,
      width:
          json['width'] != null
              ? (json['width'] as num).toDouble()
              : double.infinity,
      height: (json['height'] as num?)?.toDouble() ?? 0,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      onDateSelected:
          json['hasDateSelectedHandler'] == true
              ? (date) {
                // This would be handled by the app in a real implementation
              }
              : null,
      testDate: testDate,
      // Advanced interaction properties
      hoverColor: hoverColor,
      focusColor: focusColor,
      tooltip: tooltip,
      enableFeedback: enableFeedback,
      autofocus: autofocus,
      // JSON callback properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: {},
      // Advanced date formatting options
      useRelativeDates: useRelativeDates,
      specialDateFormats: specialDateFormats,
    );
  }

  /// Converts the CurrentDateOnlyWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    String textAlignString = 'center';
    if (textAlign == TextAlign.start || textAlign == TextAlign.left) {
      textAlignString = 'left';
    } else if (textAlign == TextAlign.end || textAlign == TextAlign.right) {
      textAlignString = 'right';
    } else if (textAlign == TextAlign.justify) {
      textAlignString = 'justify';
    }

    String fontWeightString = 'normal';
    if (fontWeight == FontWeight.bold) {
      fontWeightString = 'bold';
    } else if (fontWeight == FontWeight.w300) {
      fontWeightString = 'light';
    }

    String mainAxisAlignmentString = 'center';
    if (mainAxisAlignment == MainAxisAlignment.start) {
      mainAxisAlignmentString = 'start';
    } else if (mainAxisAlignment == MainAxisAlignment.end) {
      mainAxisAlignmentString = 'end';
    } else if (mainAxisAlignment == MainAxisAlignment.spaceBetween) {
      mainAxisAlignmentString = 'spaceBetween';
    } else if (mainAxisAlignment == MainAxisAlignment.spaceAround) {
      mainAxisAlignmentString = 'spaceAround';
    } else if (mainAxisAlignment == MainAxisAlignment.spaceEvenly) {
      mainAxisAlignmentString = 'spaceEvenly';
    }

    String crossAxisAlignmentString = 'center';
    if (crossAxisAlignment == CrossAxisAlignment.start) {
      crossAxisAlignmentString = 'start';
    } else if (crossAxisAlignment == CrossAxisAlignment.end) {
      crossAxisAlignmentString = 'end';
    } else if (crossAxisAlignment == CrossAxisAlignment.stretch) {
      crossAxisAlignmentString = 'stretch';
    } else if (crossAxisAlignment == CrossAxisAlignment.baseline) {
      crossAxisAlignmentString = 'baseline';
    }

    String? prefixIconString;
    if (prefixIcon != null) {
      prefixIconString = _getIconName(prefixIcon!);
    }

    String? suffixIconString;
    if (suffixIcon != null) {
      suffixIconString = _getIconName(suffixIcon!);
    }

    String? iconString;
    if (icon != null) {
      iconString = _getIconName(icon!);
    }

    // Convert JSON callbacks to a serializable format
    Map<String, dynamic>? serializedCallbacks;
    if (jsonCallbacks != null) {
      serializedCallbacks = {};
      jsonCallbacks!.forEach((key, value) {
        if (value is Map) {
          serializedCallbacks![key] = Map<String, dynamic>.from(value);
        } else if (value is String) {
          serializedCallbacks![key] = value;
        } else {
          // Convert other types to string representation
          serializedCallbacks![key] = value.toString();
        }
      });
    }

    return {
      'format': format,
      'showWeekday': showWeekday,
      'showYear': showYear,
      'showMonth': showMonth,
      'showDay': showDay,
      'isLiveUpdate': isLiveUpdate,
      'updateIntervalSeconds': updateIntervalSeconds,
      'locale': locale,
      'textColor': _colorToJson(textColor),
      'backgroundColor': _colorToJson(backgroundColor),
      'fontSize': fontSize,
      'fontWeight': fontWeightString,
      'textAlign': textAlignString,
      'hasBorder': hasBorder,
      'borderRadius': borderRadius,
      'borderColor': _colorToJson(borderColor),
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isCompact': isCompact,
      if (label != null) 'label': label,
      if (prefix != null) 'prefix': prefix,
      if (suffix != null) 'suffix': suffix,
      if (prefixIconString != null) 'prefixIcon': prefixIconString,
      if (suffixIconString != null) 'suffixIcon': suffixIconString,
      'isDarkTheme': isDarkTheme,
      'showIcon': showIcon,
      if (iconString != null) 'icon': iconString,
      'isAnimated': isAnimated,
      if (customText != null) 'customText': customText,
      'isUpperCase': isUpperCase,
      'isLowerCase': isLowerCase,
      'isCapitalized': isCapitalized,
      'isItalic': isItalic,
      'isBold': isBold,
      'isUnderlined': isUnderlined,
      'padding': padding,
      if (width != double.infinity) 'width': width,
      if (height != 0) 'height': height,
      'mainAxisAlignment': mainAxisAlignmentString,
      'crossAxisAlignment': crossAxisAlignmentString,
      'hasDateSelectedHandler': onDateSelected != null,
      // Advanced interaction properties
      if (hoverColor != null) 'hoverColor': hoverColor.toString(),
      if (focusColor != null) 'focusColor': focusColor.toString(),
      if (tooltip != null) 'tooltip': tooltip,
      'enableFeedback': enableFeedback,
      'autofocus': autofocus,
      // JSON callback properties
      'useJsonCallbacks': useJsonCallbacks,
      if (serializedCallbacks != null) 'callbacks': serializedCallbacks,
      // Advanced date formatting options
      'useRelativeDates': useRelativeDates,
      if (specialDateFormats != null) 'specialDateFormats': specialDateFormats,
    };
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    // Use the color value directly to avoid deprecated properties
    final hex = color
        .toString()
        .replaceAll('Color(0xff', '')
        .replaceAll(')', '');
    return '#$hex';
  }

  /// Gets an IconData from a string name
  static IconData? _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'calendar':
      case 'calendar_today':
        return Icons.calendar_today;
      case 'access_time':
      case 'clock':
        return Icons.access_time;
      case 'date_range':
        return Icons.date_range;
      case 'event':
        return Icons.event;
      case 'today':
        return Icons.today;
      case 'schedule':
        return Icons.schedule;
      case 'alarm':
        return Icons.alarm;
      case 'timer':
        return Icons.timer;
      case 'hourglass_empty':
        return Icons.hourglass_empty;
      case 'hourglass_full':
        return Icons.hourglass_full;
      case 'update':
        return Icons.update;
      case 'refresh':
        return Icons.refresh;
      case 'history':
        return Icons.history;
      case 'watch':
        return Icons.watch;
      case 'watch_later':
        return Icons.watch_later;
      case 'calendar_month':
        return Icons.calendar_month;
      case 'calendar_view_day':
        return Icons.calendar_view_day;
      case 'calendar_view_month':
        return Icons.calendar_view_month;
      case 'calendar_view_week':
        return Icons.calendar_view_week;
      default:
        return Icons.calendar_today; // Default icon
    }
  }

  /// Gets a string name from an IconData
  static String _getIconName(IconData icon) {
    if (icon == Icons.calendar_today) return 'calendar_today';
    if (icon == Icons.access_time) return 'access_time';
    if (icon == Icons.date_range) return 'date_range';
    if (icon == Icons.event) return 'event';
    if (icon == Icons.today) return 'today';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.alarm) return 'alarm';
    if (icon == Icons.timer) return 'timer';
    if (icon == Icons.hourglass_empty) return 'hourglass_empty';
    if (icon == Icons.hourglass_full) return 'hourglass_full';
    if (icon == Icons.update) return 'update';
    if (icon == Icons.refresh) return 'refresh';
    if (icon == Icons.history) return 'history';
    if (icon == Icons.watch) return 'watch';
    if (icon == Icons.watch_later) return 'watch_later';
    if (icon == Icons.calendar_month) return 'calendar_month';
    if (icon == Icons.calendar_view_day) return 'calendar_view_day';
    if (icon == Icons.calendar_view_month) return 'calendar_view_month';
    if (icon == Icons.calendar_view_week) return 'calendar_view_week';

    return 'calendar_today'; // Default icon name
  }

  @override
  State<CurrentDateOnlyWidget> createState() => _CurrentDateOnlyWidgetState();
}

class _CurrentDateOnlyWidgetState extends State<CurrentDateOnlyWidget>
    with SingleTickerProviderStateMixin {
  late DateTime _currentDateTime;
  String _formattedDate = '';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  Timer? _timer;
  bool _isHovered = false;
  bool _hasFocus = false;
  late Map<String, dynamic> _callbackState;

  @override
  void initState() {
    super.initState();
    // Use testDate if provided (for testing purposes), otherwise use current date
    _currentDateTime = widget.testDate ?? DateTime.now();
    _updateFormattedDate();

    // Set up animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.6, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.isAnimated) {
      _animationController.repeat(reverse: true);
    }

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Set up timer for live updates
    if (widget.isLiveUpdate) {
      _timer = Timer.periodic(Duration(seconds: widget.updateIntervalSeconds), (
        timer,
      ) {
        setState(() {
          // Use testDate if provided (for testing), otherwise use current date
          _currentDateTime = widget.testDate ?? DateTime.now();
          _updateFormattedDate();
        });
      });
    }
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onHover')) {
        final callback = widget.jsonCallbacks!['onHover'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: isHovered,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;

      // Call onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onFocus')) {
        final callback = widget.jsonCallbacks!['onFocus'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: hasFocus,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _updateFormattedDate() {
    // Use custom text if provided
    if (widget.customText != null) {
      _formattedDate = widget.customText!;
      return;
    }

    // Use custom date formatter if provided
    if (widget.customDateFormatter != null) {
      _formattedDate = widget.customDateFormatter!(_currentDateTime);
      return;
    }

    // Check for special date formats
    if (widget.specialDateFormats != null) {
      final dateKey =
          '${_currentDateTime.year}-${_currentDateTime.month.toString().padLeft(2, '0')}-${_currentDateTime.day.toString().padLeft(2, '0')}';
      if (widget.specialDateFormats!.containsKey(dateKey)) {
        _formattedDate = widget.specialDateFormats![dateKey]!;
        return;
      }
    }

    // Use relative dates if enabled
    if (widget.useRelativeDates) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));
      final tomorrow = today.add(const Duration(days: 1));

      final dateOnly = DateTime(
        _currentDateTime.year,
        _currentDateTime.month,
        _currentDateTime.day,
      );

      if (dateOnly == today) {
        _formattedDate = 'Today';
        return;
      } else if (dateOnly == yesterday) {
        _formattedDate = 'Yesterday';
        return;
      } else if (dateOnly == tomorrow) {
        _formattedDate = 'Tomorrow';
        return;
      }

      // Check if it's within the last week
      final difference = today.difference(dateOnly).inDays;
      if (difference > 0 && difference < 7) {
        try {
          _formattedDate = DateFormat(
            'EEEE',
            widget.locale,
          ).format(_currentDateTime);
          return;
        } catch (e) {
          // Fall back to standard formatting if this fails
        }
      }
    }

    // Use custom format if provided
    if (widget.format.isNotEmpty) {
      try {
        _formattedDate = DateFormat(
          widget.format,
          widget.locale,
        ).format(_currentDateTime);
      } catch (e) {
        _formattedDate = 'Invalid format';
      }
      return;
    }

    // Build format based on configuration
    String formatPattern = '';

    List<String> dateParts = [];

    if (widget.showWeekday) {
      dateParts.add('EEEE');
    }

    if (widget.showMonth && widget.showDay && widget.showYear) {
      dateParts.add('yMMMd');
    } else {
      if (widget.showMonth) dateParts.add('MMM');
      if (widget.showDay) dateParts.add('d');
      if (widget.showYear) dateParts.add('y');
    }

    formatPattern = dateParts.join(', ');

    if (formatPattern.isEmpty) {
      // Default format if nothing specified
      formatPattern = 'yMMMd';
    }

    try {
      _formattedDate = DateFormat(
        formatPattern,
        widget.locale,
      ).format(_currentDateTime);
    } catch (e) {
      _formattedDate = 'Invalid format';
    }
  }

  String _formatText(String text) {
    if (widget.isUpperCase) {
      text = text.toUpperCase();
    } else if (widget.isLowerCase) {
      text = text.toLowerCase();
    } else if (widget.isCapitalized) {
      text = text
          .split(' ')
          .map(
            (word) =>
                word.isNotEmpty
                    ? '${word[0].toUpperCase()}${word.substring(1)}'
                    : '',
          )
          .join(' ');
    }
    return text;
  }

  @override
  Widget build(BuildContext context) {
    // Apply text formatting
    String displayText = _formatText(_formattedDate);

    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Get responsive font size based on screen width
    double getResponsiveTitleFontSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 16.0; // Large
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium
      } else {
        return 14.0; // Default for very small screens
      }
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double responsiveTitleFontSize = getResponsiveTitleFontSize(
      screenWidth,
    );

    // Get responsive icon size based on screen width
    double getResponsiveIconSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 16.0; // Large
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium
      } else if (screenWidth >= 768) {
        return 12.0; // Small
      } else {
        return 10.0; // Extra Small (fallback for very small screens)
      }
    }

    final double responsiveIconSize = getResponsiveIconSize(screenWidth);

    // Create text style
    TextStyle textStyle =
        widget.textStyle ??
        TextStyle(
          color: effectiveTextColor,
          fontSize: widget.fontSize,
          fontWeight: widget.isBold ? FontWeight.bold : widget.fontWeight,
          fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
          decoration:
              widget.isUnderlined
                  ? TextDecoration.underline
                  : TextDecoration.none,
        );

    // Create the main content widget
    Widget content = MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor: SystemMouseCursors.basic,
      child: Focus(
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        onFocusChange: _onFocusChange,
        child: Container(
          height: _getResponsiveHeight(context),
          padding: _getResponsivePadding(context),
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border: widget.hasBorder
                ? Border.all(
                    color: _isHovered
                        ? (widget.hoverColor ?? const Color(0xFFCCCCCC))
                        : _hasFocus
                            ? (widget.focusColor ?? const Color(0xFFCCCCCC))
                            : widget.borderColor,
                    width: widget.borderWidth,
                  )
                : null,
            boxShadow: widget.hasShadow
                ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: widget.elevation,
                      offset: Offset(0, widget.elevation / 2),
                    ),
                  ]
                : null,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (widget.prefix != null) ...[
                Text(
                  widget.prefix!,
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: effectiveTextColor.withOpacity(0.7),
                    fontSize: _getResponsiveValueFontSize(context),
                  ),
                ),
                const SizedBox(width: 8),
              ],
              if (widget.prefixIcon != null) ...[
                Icon(
                  widget.prefixIcon,
                  color: effectiveTextColor,
                  size: responsiveIconSize,
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: Text(
                  displayText,
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: Color(0xFF333333),
                    fontSize: _getResponsiveValueFontSize(context),
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
              if (widget.suffix != null) ...[
                const SizedBox(width: 8),
                Text(
                  widget.suffix!,
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: effectiveTextColor.withOpacity(0.7),
                    fontSize: _getResponsiveValueFontSize(context),
                  ),
                ),
              ],
              if (widget.showIcon && widget.icon != null) ...[
                const SizedBox(width: 8),
                Icon(
                  widget.icon,
                  color: effectiveTextColor,
                  size: responsiveIconSize,
                ),
              ],
              if (widget.suffixIcon != null) ...[
                const SizedBox(width: 8),
                Icon(
                  widget.suffixIcon,
                  color: effectiveTextColor,
                  size: responsiveIconSize,
                ),
              ],
              // Always show calendar icon at the end
              const SizedBox(width: 8),
              // Icon(
              //   Icons.calendar_today,
              //   color: effectiveTextColor.withOpacity(0.7),
              //   size: responsiveIconSize,
              // ),
              SvgPicture.asset(
                    _isHovered
                        ? 'assets/images/date.svg'
                        : 'assets/images/date.svg',
                    package: 'ui_controls_library',
                    //width: responsiveIconSize,
             ),
            ],
          ),
        ),
      ),
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      content = Tooltip(message: widget.tooltip!, child: content);
    }

    // Build the final widget with label to match the desired output
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,

            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.bold,
              color: widget.isDarkTheme ? Colors.white70 : Colors.black54,
              fontSize: responsiveTitleFontSize * 0.8,
            ),
          ),
          const SizedBox(height: 4),
        ],
        content,
      ],
    );
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 4.0,
      ); // Extra Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 3.0,
      ); // Large
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 2.0,
      ); // Medium
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 40.0; // Small (768-1024px)
    } else {
      return 40.0; // Default for very small screens
    }
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
}
