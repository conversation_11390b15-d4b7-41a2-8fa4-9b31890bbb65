// entity_text_formatter.dart
import 'package:nsl/models/object_creation_model.dart';

class EntityTextFormatter {
  const EntityTextFormatter._();

  // ───────────────────── Top-level entry point ──────────────────────
  static String toNarrative(ObjectCreationModel model) {
    final sb = StringBuffer();

    // 1. Tenant
    sb.writeln('Tenant: ${model.tenant ?? ''}');

    // 2. Dynamic Entity declaration
    sb.writeln(_buildEntityDeclaration(model));
    // 3. Core metadata
    sb.writeln(getCoreMetadataSection(model));

    _appendAttributes(sb, model);

    _appendRelationships(sb, model);

    _appendBusinessRules(sb, model);

    _appendUiProperties(sb, model);

    _appendSecurity(sb, model);

    _appendEnums(sb, model);

    _appendSystemPermissions(sb, model);

    _appendRolePermissions(sb, model);

    return sb.toString().trim();
  }

  // ───────────────── Individual Section Methods ────────────────────────

  static String getEntitySection(ObjectCreationModel model) {
    final sb = StringBuffer();
    sb.writeln(getCoreMetadataSection(model));
    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + attributes section only
  static String getAttributesSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendAttributes(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + relationships section only
  static String getRelationshipsSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendRelationships(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + business rules section only
  static String getBusinessRulesSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendBusinessRules(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + UI properties section only
  static String getUiPropertiesSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendUiProperties(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + security classification section only
  static String getSecuritySection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendSecurity(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + enumerated values section only
  static String getEnumsSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendEnums(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + system permissions section only
  static String getSystemPermissionsSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendSystemPermissions(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + role permissions section only
  static String getRolePermissionsSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);
    _appendRolePermissions(sb, model);

    return sb.toString().trim();
  }

  /// Returns tenant + entity declaration + core metadata section only
  static String getCoreMetadataSection(ObjectCreationModel model) {
    final sb = StringBuffer();
    _addTenantAndDeclaration(sb, model);

    // Core metadata
    // sb.writeln(
    //     'Entity: ${model.name ?? ''} \n Entity Name: ${model.name ?? ''} \n '
    //     'Display Name: ${model.displayName ?? ''} \n Type: ${model.type ?? ''} \n '
    //     'Description: ${model.description ?? ''} \n Business Domain: ${model.businessDomain ?? ''} \n '
    //     'Category: ${model.category ?? ''} \n Tags: ${model.tags?.join(", ") ?? ''} \n '
    //     'Archival Strategy: ${model.archivalStrategy ?? ''} \n Icon: ${model.icon ?? ''} \n '
    //     'Colour Theme: ${model.colorTheme ?? ''}');
    sb.writeln(
        'Entity: ${model.name ?? ''} - Entity Name: ${model.name ?? ''} - '
        'Display Name: ${model.displayName ?? ''} - Type: ${model.type ?? ''} - '
        'Description: ${model.description ?? ''} - Business Domain: ${model.businessDomain ?? ''} - '
        'Category: ${model.category ?? ''} - Tags: ${model.tags?.join(", ") ?? ''} - '
        'Archival Strategy: ${model.archivalStrategy ?? ''} - Icon: ${model.icon ?? ''} - '
        'Colour Theme: ${model.colorTheme ?? ''}');
    return sb.toString().trim();
  }

  // ───────────────── Private Helper Methods ────────────────────────

  static void _addTenantAndDeclaration(
      StringBuffer sb, ObjectCreationModel model) {
    sb.writeln('Tenant: ${model.tenant ?? ''}');
    sb.writeln(_buildEntityDeclaration(model, forceGenerate: true));
    // sb.writeln(); // Extra blank line after declaration
  }

  static String _buildEntityDeclaration(ObjectCreationModel model,
      {bool forceGenerate = false}) {
    if (!forceGenerate && (model.entityDeclaration ?? '').isNotEmpty) {
      final txt = model.entityDeclaration!;
      return txt.trimRight().endsWith('.') ? txt : '$txt.';
    }

    if (model.attributes?.isEmpty ?? true) return '';

    final tokens = model.attributes!
        .map(_tokeniseAttribute)
        .where((t) => t.isNotEmpty)
        .join(', ');

    return '${model.displayName ?? model.name ?? 'Entity'} has $tokens.';
  }

  static String _tokeniseAttribute(ObjectAttribute a) {
    final buf = StringBuffer(a.name ?? '');

    if (a.isPrimaryKey) buf.write('^PK');
    if (a.isForeignKey) buf.write('^FK');

    if (a.required == true) buf.write('* [required]');
    if (a.unique == true && !a.isPrimaryKey) buf.write(' [unique]');

    // Default value / type hints
    if ((a.dataType ?? '').toLowerCase() == 'enum' &&
        (a.defaultValue ?? '').isNotEmpty) {
      buf.write('* (${a.defaultValue})');
    } else if ((a.defaultValue ?? '').isNotEmpty &&
        (a.defaultType ?? '').toLowerCase() == 'static_value') {
      buf.write(' [default: ${a.defaultValue}]');
    } else if ((a.defaultType ?? '').toLowerCase() == 'derived') {
      buf.write('[derived]');
    } else if ((a.defaultType ?? '').toLowerCase() == 'dependent') {
      buf.write('[dependent]');
    }

    // “information” tag for non-required, free-text fields
    if (!(a.required ?? false) &&
        !a.isPrimaryKey &&
        !a.isForeignKey &&
        (a.dataType ?? '').toLowerCase() == 'text') {
      buf.write(' [information]');
    }

    return buf.toString();
  }

  static void _appendAttributes(StringBuffer sb, ObjectCreationModel model) {
    if (model.attributes?.isEmpty ?? true) return;
    sb.writeln(
        'Attributes: Attribute Name | Display Name | Data Type | Required | Unique | '
        'Default Type | Default Value | Description | Helper Text');
    for (final a in model.attributes!) {
      sb.writeln('${a.name} | ${a.displayName ?? ''} | '
          '${a.dataType ?? ''} | ${a.required} | ${a.unique} | '
          '${a.defaultType ?? ''} | ${a.defaultValue ?? ''} | '
          '${a.description ?? ''} | ${a.helperText ?? ''}');
    }
  }

  static void _appendRelationships(StringBuffer sb, ObjectCreationModel model) {
    if (model.relationships?.isEmpty ?? true) return;
    sb.writeln(
        'Entity Relationships: Primary Entity | Related Entity | Primary Key | Foreign Key | '
        'Relationship Type | On Delete | On Update | Foreign Key Type | Description');
    for (final r in model.relationships!) {
      sb.writeln(
          '${r.primaryEntity ?? ''} | ${r.relatedEntity ?? ''} | ${r.primaryKey ?? ''} | '
          '${r.foreignKey ?? ''} | ${r.relationshipType ?? ''} | ${r.onDelete ?? ''} | '
          '${r.onUpdate ?? ''} | ${r.foreignKeyType ?? ''} | ${r.description ?? ''}');
    }
  }

  static void _appendBusinessRules(StringBuffer sb, ObjectCreationModel model) {
    if (model.businessRules?.isEmpty ?? true) return;
    sb.writeln(
        'Attribute Business Rules: Entity Name | Attribute Name | Left Operand | Operator | '
        'Right Operand | Success Value/Range | Warning Value/Range | Failure Value/Range | '
        'Multi Condition Operator | Warning Message | Success Message | Error Message');
    for (final b in model.businessRules!) {
      sb.writeln(
          '${b.entityName ?? ''} | ${b.attributeName ?? ''} | ${b.leftOperand ?? ''} | '
          '${b.operator ?? ''} | ${b.rightOperand ?? ''} | ${b.successValueRange ?? ''} | '
          '${b.warningValueRange ?? ''} | ${b.failureValueRange ?? ''} | '
          '${b.multiConditionOperator ?? ''} | ${b.warningMessage ?? ''} | '
          '${b.successMessage ?? ''} | ${b.errorMessage ?? ''}');
    }
  }

  static void _appendUiProperties(StringBuffer sb, ObjectCreationModel model) {
    if (model.uiProperties?.isEmpty ?? true) return;
    sb.writeln(
        'UI Properties: Entity.Attribute | Control Type | Display Format | Input Mask | '
        'Placeholder Text | Auto Complete | Read Only | Validation Display | '
        'Help Text Position | Label | Required Indicator');
    for (final u in model.uiProperties!) {
      sb.writeln('${u.entityAttribute ?? ''} | ${u.controlType ?? ''} | '
          '${u.displayFormat ?? ''} | ${u.inputMask ?? ''} | '
          '${u.placeholderText ?? ''} | ${u.autoComplete ?? false} | '
          '${u.readOnly ?? false} | ${u.validationDisplay ?? ''} | '
          '${u.helpTextPosition ?? ''} | ${u.label ?? ''} | '
          '${u.requiredIndicator ?? false}');
    }
  }

  static void _appendSecurity(StringBuffer sb, ObjectCreationModel model) {
    if (model.securityClassification?.isEmpty ?? true) return;
    sb.writeln(
        'Security Classification: Entity.Attribute | Classification | PII Type | '
        'Encryption Required | Encryption Type | Masking Required | Masking Pattern | '
        'Access Level | Audit Trail | Data Residency | Retention Override | '
        'Anonymization Required | Anonymization Method | Compliance Frameworks');
    for (final s in model.securityClassification!) {
      sb.writeln('${s.entityAttribute ?? ''} | ${s.classification ?? ''} | '
          '${s.piiType ?? ''} | ${s.encryptionRequired ?? false} | '
          '${s.encryptionType ?? ''} | ${s.maskingRequired ?? false} | '
          '${s.maskingPattern ?? ''} | ${s.accessLevel ?? ''} | '
          '${s.auditTrail ?? false} | ${s.dataResidency ?? ''} | '
          '${s.retentionOverride ?? ''} | ${s.anonymizationRequired ?? false} | '
          '${s.anonymizationMethod ?? ''} | ${s.complianceFrameworks?.join(', ') ?? ''}');
    }
  }

  static void _appendEnums(StringBuffer sb, ObjectCreationModel model) {
    if (model.enumValues?.isEmpty ?? true) return;
    sb.writeln(
        'Enumerated Values: Entity.Attribute | Enum Name | Value | Display | '
        'Description | Sort Order | Active');
    for (final e in model.enumValues!) {
      if (e is Map<String, dynamic>) {
        sb.writeln('${e.entityAttribute ?? ''} | ${e.enumName ?? ''} | '
            '${e.value ?? ''} | ${e.display ?? ''} | '
            '${e.description ?? ''} | ${e.sortOrder ?? 0} | '
            '${e.active ?? false}');
      }
    }
  }

  static void _appendSystemPermissions(
      StringBuffer sb, ObjectCreationModel model) {
    if (model.systemPermissions?.isEmpty ?? true) return;
    sb.writeln(
        'System Permissions: permission_id | permission_name | permission_type | '
        'resource_identifier | actions | description | scope | natural_language | version | status');
    for (final p in model.systemPermissions!) {
      if (p is Map<String, dynamic>) {
        sb.writeln('${p.permissionId ?? ''} | ${p.permissionName ?? ''} | '
            '${p.permissionType ?? ''} | ${p.resourceIdentifier ?? ''} | '
            '[${p.actions?.join(', ') ?? ''}] | ${p.description ?? ''} | '
            '${p.scope ?? ''} | ${p.naturalLanguage ?? ''} | '
            '${p.version ?? 0} | ${p.status ?? ''}');
      }
    }
  }

  static void _appendRolePermissions(
      StringBuffer sb, ObjectCreationModel model) {
    if (model.roleSystemPermissions?.isEmpty ?? true) return;
    sb.writeln(
        'Role System Permissions: role_id | permission_id | granted_actions | '
        'row_level_conditions | natural_language | version | status');
    for (final rp in model.roleSystemPermissions!) {
      if (rp is Map<String, dynamic>) {
        sb.writeln('${rp.roleId ?? ''} | ${rp.permissionId ?? ''} | '
            '[${rp.grantedActions?.join(', ') ?? ''}] | ${rp.rowLevelConditions ?? ''} | '
            '${rp.naturalLanguage ?? ''} | ${rp.version ?? 0} | '
            '${rp.status ?? ''}');
      }
    }
  }
}
