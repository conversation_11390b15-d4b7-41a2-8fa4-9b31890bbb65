import 'package:dio/dio.dart';
import '../models/nsl_hub_java_box_model.dart';
import '../models/java_file_model.dart';
import 'base_api_service.dart';
import '../utils/logger.dart';

/// Service for handling NSL Hub Java Box Grouped API operations
class NslHubJavaBoxGroupedService extends BaseApiService {
  static const String _baseUrl = 'http://10.26.1.52:8084';
  static const String _groupedEndpoint = '/api/nslhub-java-box/grouped';

  /// Fetch grouped NSL Hub Java Box data by goId
  Future<NslHubJavaBoxModel> getGroupedDataByGoId(String goId) async {
    try {
      Logger.info('Fetching grouped NSL Hub Java Box data for goId: $goId');

      // Build the full URL with query parameter
      final fullUrl = '$_baseUrl$_groupedEndpoint?goId=$goId';

      final response = await dio.get(fullUrl);

      Logger.info('Grouped data fetched successfully: ${response.statusCode}');
      Logger.info('Response data type: ${response.data.runtimeType}');
      Logger.info('Response data: ${response.data}');

      if (response.data != null) {
        return NslHubJavaBoxModel.fromJson(response.data);
      } else {
        Logger.error('Empty response received');
        throw Exception('Empty response');
      }
    } catch (e) {
      Logger.error('Error fetching grouped data for goId $goId: $e');
      rethrow;
    }
  }

  /// Fetch grouped data with error handling and fallback
  Future<NslHubJavaBoxModel> getGroupedDataByGoIdWithFallback(String goId) async {
    try {
      return await getGroupedDataByGoId(goId);
    } catch (e) {
      Logger.error('Primary API call failed, returning empty model: $e');
      // Return empty model as fallback
      return NslHubJavaBoxModel(
        success: false,
        message: "Error occurred: $e",
        data: [],
        totalGroups: 0,
        totalItems: 0,
      );
    }
  }

  /// Group data by nslGroup for easier UI consumption
  Map<String, List<NslHubJavaBoxData>> groupDataByNslGroup(List<NslHubJavaBoxData> data) {
    Map<String, List<NslHubJavaBoxData>> groupedData = {};
    
    for (var item in data) {
      String groupKey = item.nslGroup ?? 'Unknown';
      
      if (!groupedData.containsKey(groupKey)) {
        groupedData[groupKey] = [];
      }
      
      groupedData[groupKey]!.add(item);
    }
    
    return groupedData;
  }

  /// Get display text for an item (naturalLanguage if available, otherwise nslName)
  String getDisplayText(NslHubJavaBoxData item) {
    if (item.naturalLanguage != null && item.naturalLanguage!.isNotEmpty) {
      return item.naturalLanguage!;
    }
    return item.nslName ?? 'Unnamed Item';
  }

  /// Parse line numbers from startLines and endLines strings
  List<int> parseLineNumbers(NslHubJavaBoxData item) {
    List<int> lineNumbers = [];
    
    String? startLines = item.startLines;
    String? endLines = item.endLines;
    
    if (startLines == null || startLines.isEmpty) {
      return lineNumbers;
    }
    
    // Split by comma and process each part
    List<String> startParts = startLines.split(',');
    List<String> endParts = endLines?.split(',') ?? [];
    
    for (int i = 0; i < startParts.length; i++) {
      String startPart = startParts[i].trim();
      String endPart = i < endParts.length ? endParts[i].trim() : startPart;
      
      int? start = int.tryParse(startPart);
      int? end = int.tryParse(endPart);
      
      if (start != null && end != null) {
        if (start == end) {
          // Single line
          lineNumbers.add(start);
        } else {
          // Range of lines
          for (int line = start; line <= end; line++) {
            lineNumbers.add(line);
          }
        }
      } else if (start != null) {
        // Only start line available
        lineNumbers.add(start);
      }
    }
    
    // Remove duplicates and sort
    lineNumbers = lineNumbers.toSet().toList();
    lineNumbers.sort();
    
    return lineNumbers;
  }

  /// Search items by name or natural language
  Future<List<NslHubJavaBoxData>> searchItems(String goId, String searchTerm) async {
    try {
      final response = await getGroupedDataByGoId(goId);
      
      if (response.data != null) {
        return response.data!
            .where((item) => 
                (item.nslName != null && 
                 item.nslName!.toLowerCase().contains(searchTerm.toLowerCase())) ||
                (item.naturalLanguage != null && 
                 item.naturalLanguage!.toLowerCase().contains(searchTerm.toLowerCase())))
            .toList();
      }
      
      return [];
    } catch (e) {
      Logger.error('Error searching items: $e');
      rethrow;
    }
  }

  /// Get items by nslGroup
  Future<List<NslHubJavaBoxData>> getItemsByGroup(String goId, String nslGroup) async {
    try {
      final response = await getGroupedDataByGoId(goId);
      
      if (response.data != null) {
        return response.data!
            .where((item) => 
                item.nslGroup != null && 
                item.nslGroup!.toLowerCase() == nslGroup.toLowerCase())
            .toList();
      }
      
      return [];
    } catch (e) {
      Logger.error('Error getting items by group: $e');
      rethrow;
    }
  }

  // ========== JAVA FILE API METHODS ==========

  static const String _javaFilesEndpoint = '/api/java-files/read';

  /// Fetch Java file content by goId
  Future<JavaFileModel> getJavaFileByGoId(String goId) async {
    try {
      Logger.info('Fetching Java file for goId: $goId');

      // Build the full URL with query parameter
      final fullUrl = '$_baseUrl$_javaFilesEndpoint?goId=$goId';

      final response = await dio.get(fullUrl);

      Logger.info('Java file fetched successfully: ${response.statusCode}');
      Logger.info('Response data type: ${response.data.runtimeType}');

      if (response.data != null) {
        return JavaFileModel.fromJson(response.data);
      } else {
        Logger.error('Empty response received for Java file');
        throw Exception('Empty response');
      }
    } catch (e) {
      Logger.error('Error fetching Java file for goId $goId: $e');
      rethrow;
    }
  }

  /// Fetch Java file with error handling and fallback
  Future<JavaFileModel> getJavaFileByGoIdWithFallback(String goId) async {
    try {
      return await getJavaFileByGoId(goId);
    } catch (e) {
      Logger.error('Java file API call failed, returning empty model: $e');
      // Return empty model as fallback
      return JavaFileModel(
        fileName: 'Error.java',
        fileType: 'java',
        solution: Solution(
          java: Java(lines: []),
        ),
      );
    }
  }

  /// Get Java lines as a list for easier UI consumption
  List<JavaLine> getJavaLines(JavaFileModel javaFile) {
    return javaFile.solution?.java?.lines ?? [];
  }

  /// Search Java lines by content
  List<JavaLine> searchJavaLines(JavaFileModel javaFile, String searchTerm) {
    final lines = getJavaLines(javaFile);
    return lines
        .where((line) => 
            line.content != null && 
            line.content!.toLowerCase().contains(searchTerm.toLowerCase()))
        .toList();
  }

  /// Get Java lines by line number range
  List<JavaLine> getJavaLinesByRange(JavaFileModel javaFile, int startLine, int endLine) {
    final lines = getJavaLines(javaFile);
    return lines
        .where((line) => 
            line.lineNo != null && 
            line.lineNo! >= startLine && 
            line.lineNo! <= endLine)
        .toList();
  }

  /// Get specific Java lines by line numbers
  List<JavaLine> getJavaLinesByNumbers(JavaFileModel javaFile, List<int> lineNumbers) {
    final lines = getJavaLines(javaFile);
    return lines
        .where((line) => 
            line.lineNo != null && 
            lineNumbers.contains(line.lineNo!))
        .toList();
  }
}
