import 'dart:math' as math;

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/books/all_global_objective_model.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/global_objectives_provider.dart';
import 'package:nsl/screens/new_design/my_business/collection.dart';
import 'package:nsl/screens/new_design/my_business/solution_widgets.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MyBusinessSolution extends StatefulWidget {
  const MyBusinessSolution({super.key});

  @override
  State<MyBusinessSolution> createState() => _MyBusinessSolutionState();
}

final List<String> bannerImages = [
  'assets/images/my_business/collections/my_business_carousel_one.jpg',
  'assets/images/my_business/collections/my_business_carousel_two.jpg',
  'assets/images/my_business/collections/my_business_carousel_three.jpg',
];
final CarouselSliderController carouselController = CarouselSliderController();
final TextEditingController _searchController = TextEditingController();
List<Objective> _objectives = [];
List<Objective> _filteredObjectives = [];
const int cardsPerRow = 3;
const int rowsPerPage = 3;
const int cardsPerPage = cardsPerRow * rowsPerPage;
const int itemsPerPage = 9; // cardsPerPage
bool _showSearchBar = false;
int _currentPage = 0;
int _currentCollectionPage = 1;

class _MyBusinessSolutionState extends State<MyBusinessSolution> {
  bool _isLoading = true;
  final List<Objective> fallbackData = [
    Objective(
      goId: "obj_1",
      name: "Apply Leave",
      description: "Process for applying leave",
      status: "active",
    ),
    Objective(
      goId: "obj_2",
      name: "Employee Details",
      description: "View and manage employee information",
      status: "active",
    ),
    Objective(
      goId: "obj_3",
      name: "My completed Module",
      description: "Track completed training modules",
      status: "active",
    ),
    Objective(
      goId: "obj_4",
      name: "My Attendance",
      description: "View attendance records",
      status: "active",
    ),
    Objective(
      goId: "obj_5",
      name: "Organisation Policy Details",
      description: "Access company policies",
      status: "active",
    ),
    Objective(
      goId: "obj_6",
      name: "Product Details",
      description: "View product information",
      status: "active",
    ),
    Objective(
      goId: "obj_7",
      name: "Product Details",
      description: "View product information",
      status: "active",
    ),
    Objective(
      goId: "obj_8",
      name: "Product Details",
      description: "View product information",
      status: "active",
    ),
    Objective(
      goId: "obj_9",
      name: "Product Details",
      description: "View product information",
      status: "active",
    ),
    Objective(
      goId: "obj_10",
      name: "Product Details",
      description: "View product information",
      status: "active",
    ),
  ];
  late PageController _pageController;
  @override
  void initState() {
    super.initState();
    // Load objectives data using the method from web_transaction_solution
    _loadObjectivesData();
    // Add listener to search controller
    _searchController.addListener(_onSearchChanged);
    _pageController = PageController();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _pageController.dispose();
    super.dispose();
  }

  /// Load objectives data from Global Objectives API and fallback to local data
  Future<void> _loadObjectivesData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // First, try to load data from Global Objectives API
      // try {
      //   final globalObjectivesProvider = GlobalObjectivesProvider();
      //   await globalObjectivesProvider.fetchGlobalObjectives();

      //   if (globalObjectivesProvider.objectives.isNotEmpty) {
      //     setState(() {
      //       _objectives = globalObjectivesProvider.objectives;
      //       _filteredObjectives = List.from(_objectives);
      //       _isLoading = false;
      //     });
      //     return;
      //   }
      // } catch (apiError) {
      //   debugPrint('Error loading from Global Objectives API: $apiError');
      //   // Continue to fallback options
      // }

      // Define fallback data in case API loading fails
      final List<Objective> fallbackData = [
        Objective(
          goId: "obj_1",
          name: "Apply Leave",
          description: "Process for applying leave",
          status: "active",
        ),
        Objective(
          goId: "obj_2",
          name: "Employee Details",
          description: "View and manage employee information",
          status: "active",
        ),
        Objective(
          goId: "obj_3",
          name: "My completed Module",
          description: "Track completed training modules",
          status: "active",
        ),
        Objective(
          goId: "obj_4",
          name: "My Attendance",
          description: "View attendance records",
          status: "active",
        ),
        Objective(
          goId: "obj_5",
          name: "Organisation Policy Details",
          description: "Access company policies",
          status: "active",
        ),
        Objective(
          goId: "obj_6",
          name: "Product Details",
          description: "View product information",
          status: "active",
        ),
        Objective(
          goId: "obj_7",
          name: "Training Management",
          description: "Manage training programs",
          status: "active",
        ),
        Objective(
          goId: "obj_8",
          name: "Asset Management",
          description: "Track company assets",
          status: "active",
        ),
        Objective(
          goId: "obj_9",
          name: "Expense Tracking",
          description: "Monitor expenses",
          status: "active",
        ),
        Objective(
          goId: "obj_10",
          name: "Quality Assurance",
          description: "Ensure quality standards",
          status: "active",
        ),
      ];

      setState(() {
        _objectives = fallbackData;
        _filteredObjectives = List.from(_objectives);
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error in _loadObjectivesData: $e');
      setState(() {
        _objectives = [];
        _filteredObjectives = [];
        _isLoading = false;
      });
    }
  }

  void _onSearchChanged() {
    setState(() {
      if (_searchController.text.isEmpty) {
        // Show all objectives when search is empty
        _filteredObjectives = List.from(_objectives);
      } else {
        // Filter objectives based on search text
        _filteredObjectives = _objectives
            .where((objective) =>
                (objective.name
                        ?.toLowerCase()
                        .contains(_searchController.text.toLowerCase()) ??
                    false) ||
                (objective.description
                        ?.toLowerCase()
                        .contains(_searchController.text.toLowerCase()) ??
                    false))
            .toList();
      }
    });
  }

  List<Objective> _getCurrentPageItems() {
    if (_filteredObjectives.isEmpty) return [];

    final startIndex = (_currentCollectionPage - 1) * itemsPerPage;
    final endIndex = startIndex + itemsPerPage;

    if (startIndex >= _filteredObjectives.length) return [];

    return _filteredObjectives.sublist(
        startIndex,
        endIndex > _filteredObjectives.length
            ? _filteredObjectives.length
            : endIndex);
  }

  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar and reset to all objectives
        _searchController.clear();
        _filteredObjectives = List.from(_objectives);
      }
    });
  }

  Widget _buildNoResultsWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No collections found',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s18,
              fontWeight: FontManager.medium,
              color: Colors.grey.shade600,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try searching with different keywords',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              color: Colors.grey.shade500,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Color(0xffF7F9FB),
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        title: Text(''),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      drawer: CustomDrawer(),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
          child: Column(
            children: [
              /// Carousel Banner
              CarouselSlider.builder(
                itemCount: bannerImages.length,
                itemBuilder: (context, index, realIndex) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(AppSpacing.sm),
                    child: Image.asset(
                      bannerImages[index],
                      fit: BoxFit.cover,
                      width: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: MediaQuery.of(context).size.height / 2,
                          color: Colors.grey[200],
                          child: Center(
                            child: Icon(Icons.broken_image,
                                color: Colors.grey[400], size: 48),
                          ),
                        );
                      },
                    ),
                  );
                },
                options: CarouselOptions(
                  height: MediaQuery.of(context).size.height / 5.5,
                  viewportFraction: 1.0,
                  autoPlay: true,
                  autoPlayInterval: Duration(seconds: 5),
                  onPageChanged: (index, reason) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                ),
              ),

              SizedBox(height: AppSpacing.xs),

              AnimatedSmoothIndicator(
                activeIndex: _currentPage,
                count: bannerImages.length,
                effect: SlideEffect(
                  dotHeight: 8,
                  dotWidth: 8,
                  spacing: 10,
                  activeDotColor: Color(0xff0058FF),
                  dotColor: Colors.grey.shade300,
                ),
              ),

              SizedBox(height: AppSpacing.xs),

              _showSearchBar
                  ? FullWidthSearchBar(
                      controller: _searchController,
                      onClose: _toggleSearchBar,
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppLocalizations.of(context)
                              .translate('myBusinessSolutions.solutions'),
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s16,
                            fontWeight: FontManager.medium,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                        GestureDetector(
                          onTap: _toggleSearchBar,
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: Container(
                              // margin:
                              //     const EdgeInsets.only(right: AppSpacing.sm),
                              height: 36,
                              child: HoverableSearchIcon(),
                            ),
                          ),
                        ),
                      ],
                    ),

              SizedBox(height: AppSpacing.xs),

              /// Grid Area
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredObjectives.isEmpty
                        ? _buildNoResultsWidget()
                        : PageView.builder(
                            controller: _pageController,
                            itemCount:
                                (_filteredObjectives.length / cardsPerPage)
                                    .ceil(),
                            itemBuilder: (context, pageIndex) {
                              final startIndex = pageIndex * cardsPerPage;
                              final endIndex = (startIndex + cardsPerPage)
                                  .clamp(0, _filteredObjectives.length);
                              final currentItems = _filteredObjectives.sublist(
                                  startIndex, endIndex);

                              return SingleChildScrollView(
                                child: Column(
                                  children:
                                      List.generate(rowsPerPage, (rowIndex) {
                                    final rowStartIndex =
                                        rowIndex * cardsPerRow;

                                    return Padding(
                                      padding: const EdgeInsets.only(
                                          bottom: AppSpacing.sm),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: List.generate(cardsPerRow,
                                            (colIndex) {
                                          final index =
                                              rowStartIndex + colIndex;
                                          if (index >= currentItems.length) {
                                            return const Expanded(
                                                child: SizedBox());
                                          }

                                          final objective = currentItems[index];
                                          double screenHeight =
                                              MediaQuery.of(context)
                                                  .size
                                                  .height;
                                          double cardHeight;

                                          if (screenHeight <= 600) {
                                            cardHeight = screenHeight / 5.7;
                                          } else if (screenHeight <= 700) {
                                            cardHeight = screenHeight / 5.7;
                                          } else if (screenHeight <= 800) {
                                            cardHeight = screenHeight / 5.5;
                                          } else if (screenHeight <= 900) {
                                            cardHeight = screenHeight / 5.4;
                                          } else if (screenHeight <= 1000) {
                                            cardHeight = screenHeight / 5.2;
                                          } else {
                                            cardHeight = screenHeight / 5.2;
                                          }
                                          // print((index + 1) % cardsPerRow);
                                          double marginRight = 0;
                                          double marginLeft = 0;
                                          if ((index + 1) % cardsPerRow == 0) {
                                            marginLeft = AppSpacing.sm;
                                          } else if ((index + 1) %
                                                  cardsPerRow ==
                                              2) {
                                            marginLeft = 5;
                                            marginRight = 5;
                                          } else {
                                            marginRight = AppSpacing.sm;
                                          }

                                          return Expanded(
                                            child: Container(
                                              margin: EdgeInsets.only(
                                                right: marginRight,
                                                left: marginLeft,
                                              ),
                                              height: cardHeight,
                                              child: ObjectiveCard(
                                                  objective: objective),
                                            ),
                                          );
                                        }),
                                      ),
                                    );
                                  }),
                                ),
                              );
                            },
                          ),
              ),

              /// Bottom Smooth Page Indicator
              if (_filteredObjectives.isNotEmpty && !isKeyboardVisible)
                Padding(
                  padding: const EdgeInsets.only(bottom: AppSpacing.xs),
                  child: SmoothPageIndicator(
                    controller: _pageController,
                    count: (_filteredObjectives.length / cardsPerPage).ceil(),
                    effect: WormEffect(
                      dotHeight: 8,
                      dotWidth: 8,
                      spacing: 10,
                      activeDotColor: Color(0xff0058FF),
                      dotColor: Colors.grey.shade300,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class ObjectiveCard extends StatefulWidget {
  final Objective objective;

  const ObjectiveCard({
    super.key,
    required this.objective,
  });

  @override
  _ObjectiveCardState createState() => _ObjectiveCardState();
}

class _ObjectiveCardState extends State<ObjectiveCard> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SolutionWidgets(),
          ),
        );
      },
      child: Card(
        margin: EdgeInsets.zero,
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.xs),
          side: BorderSide(
            color: Color(0xffD0D0D0),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 4,
                child: Container(
                  alignment: Alignment.topCenter,
                  decoration: BoxDecoration(
                    // color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomImage.asset(
                    "assets/images/my_business/solutions/solution_apply.png",
                  ).toWidget(fit: BoxFit.cover),
                ),
              ),
              Expanded(
                flex: 2,
                child: Container(
                  alignment: Alignment.topLeft,
                  child: Text(
                    widget.objective.name ?? 'Unknown Objective',
                    style: FontManager.getCustomStyle(
                        fontSize: FontManager.s13,
                        fontWeight: FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Full width search bar widget that appears when search icon is clicked

class FullWidthSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const FullWidthSearchBar({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  State<FullWidthSearchBar> createState() => _FullWidthSearchBarState();
}

class _FullWidthSearchBarState extends State<FullWidthSearchBar> {
  final FocusNode _focusNode = FocusNode();
  bool _isSearchFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isSearchFocused = _focusNode.hasFocus;
      });
    });
    // Auto-focus when search bar is activated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius:
            BorderRadius.circular(AppSpacing.xs), // More rounded border
        border: Border.all(color: Colors.grey.shade300, width: 0.5),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: widget.controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0),
                isDense: true,
                hintText: 'Search',
                hintStyle: FontManager.getCustomStyle(
                  color: Color(0xffD0D0D0),
                  fontSize: FontManager.s14,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontFamily: FontManager.fontFamilyTiemposText),
              // Handle keyboard submit action - only dismiss keyboard, keep search results
              onSubmitted: (value) {
                FocusScope.of(context).unfocus();
              },
            ),
          ),

          // Search/Close icon on the right
          GestureDetector(
            onTap: () {
              if (_isSearchFocused) {
                // When focused, close icon is shown - close search bar
                FocusScope.of(context).unfocus();
                widget.onClose();
              } else {
                // When not focused, search icon is shown - focus search field
                _focusNode.requestFocus();
              }
            },
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: ConstrainedBox(
                constraints: BoxConstraints.tightFor(
                    width: 40, height: 40), // Tap target size
                child: Align(
                  alignment: Alignment.center,
                  child: _isSearchFocused
                      ? Icon(
                          Icons.close,
                          size: 20,
                          color: Colors.grey.shade600,
                        )
                      : CustomImage.asset(
                          'assets/images/my_business/search_collection.svg',
                          width: 20,
                          height: 20,
                          fit: BoxFit.contain,
                          color: Colors.grey.shade600,
                        ).toWidget(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  HoverableSearchIconState createState() => HoverableSearchIconState();
}

class HoverableSearchIconState extends State<HoverableSearchIcon> {
  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints:
          const BoxConstraints.tightFor(width: 40, height: 40), // Touch area
      child: Align(
        alignment: Alignment.center,
        child: CustomImage.asset(
          'assets/images/my_business/search_collection.svg',
          width: 20,
          height: 20,
          fit: BoxFit.contain,
          color: Colors.black,
        ).toWidget(),
      ),
    );
  }
}
