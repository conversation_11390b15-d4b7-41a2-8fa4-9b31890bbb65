import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'dart:async';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ProgressBarColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Types of progress bar
enum ProgressBarType {
  /// A linear (horizontal) progress bar
  linear,

  /// A circular progress indicator
  circular,
}

/// Progress bar states for different visual representations
enum ProgressBarState {
  /// Default state - basic progress bar
  defaultState,

  /// Loading state - progress bar with loading styling
  loading,

  /// Tooltip state - shows tooltip on hover
  tooltip,

  /// Indeterminate state - animated progress bar
  indeterminate,

  /// Finished state - progress bar at 100%
  finished,

  /// With class state - final state with percentage labels
  withClass,
}

/// Animation direction for the progress bar
enum AnimationDirection {
  /// Progress increases from left to right (or clockwise for circular)
  forward,

  /// Progress increases from right to left (or counter-clockwise for circular)
  reverse,
}

/// A customizable progress bar widget.
///
/// This widget provides a progress indicator with various customization options
/// including appearance, behavior, and animation.
class ProgressBarWidget extends StatefulWidget {
  /// The current progress value (between 0.0 and 1.0)
  final double? value;

  /// The type of progress bar (linear or circular)
  final ProgressBarType type;

  /// The state of the progress bar
  final ProgressBarState progressState;

  /// Whether the progress bar is indeterminate
  final bool indeterminate;

  /// Custom tooltip text (if null, will show percentage)
  final String? tooltipText;

  /// Whether to show tooltip on hover
  final bool showTooltipOnHover;

  /// The color of the progress indicator
  final Color color;

  /// The background color of the progress track
  final Color backgroundColor;

  /// The height/thickness of the progress bar
  final double thickness;

  /// The width of the progress bar (for linear type)
  final double? width;

  /// The height of the progress bar (for linear type)
  final double? height;

  /// The size of the circular progress indicator (for circular type)
  final double? size;

  /// The border radius of the linear progress bar
  final double borderRadius;

  /// Whether to show a label with the progress value
  final bool showLabel;

  /// The format of the label (e.g., "50%" or "3/6")
  final String Function(double)? labelFormat;

  /// The style of the label text
  final TextStyle? labelStyle;

  /// The position of the label (above, below, center, etc.)
  final LabelPosition labelPosition;

  /// Whether to animate changes in the progress value
  final bool animate;

  /// The duration of the animation
  final Duration animationDuration;

  /// The curve of the animation
  final Curve animationCurve;

  /// The direction of the animation
  final AnimationDirection animationDirection;

  /// Whether to show a border around the progress bar
  final bool hasBorder;

  /// The color of the border
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// Whether to show a shadow under the progress bar
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The padding around the progress bar
  final EdgeInsetsGeometry padding;

  /// The margin around the progress bar
  final EdgeInsetsGeometry margin;

  /// The alignment of the progress bar within its container
  final Alignment alignment;

  /// The maximum value represented by the progress bar
  final double maxValue;

  /// The minimum value represented by the progress bar
  final double minValue;

  /// The current value in absolute terms (will be converted to a proportion)
  final double? currentValue;

  /// Whether to show min/max labels
  final bool showMinMaxLabels;

  /// The style of the min/max labels
  final TextStyle? minMaxLabelStyle;

  /// The prefix to show before the label (e.g., "$" or "€")
  final String? prefix;

  /// The suffix to show after the label (e.g., "%" or "MB")
  final String? suffix;

  /// The number of decimal places to show in the label
  final int decimalPlaces;

  /// Whether the progress bar is disabled
  final bool isDisabled;

  /// Whether to show a gradient in the progress bar
  final bool hasGradient;

  /// The colors to use in the gradient
  final List<Color>? gradientColors;

  /// The stops for the gradient colors
  final List<double>? gradientStops;

  /// Whether to pulse/throb the progress bar when indeterminate
  final bool pulseWhenIndeterminate;

  /// The callback when the progress bar is tapped
  final VoidCallback? onTap;

  /// Whether the progress bar is interactive
  final bool isInteractive;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Progress-specific JSON configuration
  /// Whether to use JSON progress configuration
  final bool useJsonProgressConfig;

  /// Progress-specific JSON configuration
  final Map<String, dynamic>? progressConfig;

  /// Creates a progress bar widget.
  const ProgressBarWidget({
    super.key,
    this.value,
    this.type = ProgressBarType.linear,
    this.progressState = ProgressBarState.defaultState,
    this.indeterminate = false,
    this.tooltipText,
    this.showTooltipOnHover = false,
    this.color = Colors.blue,
    this.backgroundColor = const Color(0xFFE0E0E0),
    this.thickness = 4.0,
    this.width,
    this.height,
    this.size,
    this.borderRadius = 4.0,
    this.showLabel = false,
    this.labelFormat,
    this.labelStyle,
    this.labelPosition = LabelPosition.below,
    this.animate = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.animationDirection = AnimationDirection.forward,
    this.hasBorder = false,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.padding = const EdgeInsets.only(top: 12),
    this.margin = const EdgeInsets.all(0),
    this.alignment = Alignment.center,
    this.maxValue = 100.0,
    this.minValue = 0.0,
    this.currentValue,
    this.showMinMaxLabels = false,
    this.minMaxLabelStyle,
    this.prefix,
    this.suffix,
    this.decimalPlaces = 0,
    this.isDisabled = false,
    this.hasGradient = false,
    this.gradientColors,
    this.gradientStops,
    this.pulseWhenIndeterminate = true,
    this.onTap,
    this.isInteractive = false,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Progress-specific JSON configuration
    this.useJsonProgressConfig = false,
    this.progressConfig,
  });

  /// Creates a ProgressBarWidget from a JSON map
  ///
  /// This factory constructor allows for creating a ProgressBarWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory ProgressBarWidget.fromJson(Map<String, dynamic> json) {
    // Parse progress bar type
    ProgressBarType type = ProgressBarType.linear;
    if (json.containsKey('type') && json['type'] == 'circular') {
      type = ProgressBarType.circular;
    }

    // Parse label position
    LabelPosition labelPosition = LabelPosition.below;
    if (json.containsKey('labelPosition')) {
      final String posStr = json['labelPosition'].toString().toLowerCase();
      if (posStr == 'above') {
        labelPosition = LabelPosition.above;
      } else if (posStr == 'left') {
        labelPosition = LabelPosition.left;
      } else if (posStr == 'right') {
        labelPosition = LabelPosition.right;
      } else if (posStr == 'center') {
        labelPosition = LabelPosition.center;
      }
    }

    // Parse animation direction
    AnimationDirection animationDirection = AnimationDirection.forward;
    if (json.containsKey('animationDirection') &&
        json['animationDirection'] == 'reverse') {
      animationDirection = AnimationDirection.reverse;
    }

    return ProgressBarWidget(
      value: json['value'] != null ? (json['value'] as num).toDouble() : null,
      type: type,
      indeterminate: json['indeterminate'] as bool? ?? false,
      color:
          json.containsKey('color') ? _parseColor(json['color']) : Colors.blue,
      backgroundColor:
          json.containsKey('backgroundColor')
              ? _parseColor(json['backgroundColor'])
              : const Color(0xFFE0E0E0),
      thickness:
          json['thickness'] != null
              ? (json['thickness'] as num).toDouble()
              : 4.0,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      size: json['size'] != null ? (json['size'] as num).toDouble() : null,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      showLabel: json['showLabel'] as bool? ?? false,
      labelPosition: labelPosition,
      animate: json['animate'] as bool? ?? true,
      animationDirection: animationDirection,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor:
          json.containsKey('borderColor')
              ? _parseColor(json['borderColor'])
              : Colors.grey,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      maxValue:
          json['maxValue'] != null
              ? (json['maxValue'] as num).toDouble()
              : 100.0,
      minValue:
          json['minValue'] != null ? (json['minValue'] as num).toDouble() : 0.0,
      currentValue:
          json['currentValue'] != null
              ? (json['currentValue'] as num).toDouble()
              : null,
      showMinMaxLabels: json['showMinMaxLabels'] as bool? ?? false,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      decimalPlaces: json['decimalPlaces'] as int? ?? 0,
      isDisabled: json['isDisabled'] as bool? ?? false,
      hasGradient: json['hasGradient'] as bool? ?? false,
      pulseWhenIndeterminate: json['pulseWhenIndeterminate'] as bool? ?? true,
      isInteractive: json['isInteractive'] as bool? ?? false,
      autofocus: json['autofocus'] as bool? ?? false,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonProgressConfig: json['useJsonProgressConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
      semanticsLabel: json['semanticsLabel'] as String?,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return Colors.blue;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.blue; // Default color
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'value': value,
      'type': type.toString().split('.').last,
      'indeterminate': indeterminate,
      'color': '#${color.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'thickness': thickness,
      'width': width,
      'height': height,
      'size': size,
      'borderRadius': borderRadius,

      // Label properties
      'showLabel': showLabel,
      'labelPosition': labelPosition.toString().split('.').last,

      // Animation properties
      'animate': animate,
      'animationDuration': animationDuration.inMilliseconds,
      'animationDirection': animationDirection.toString().split('.').last,

      // Border properties
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,

      // Shadow properties
      'hasShadow': hasShadow,
      'elevation': elevation,

      // Layout properties
      'alignment': alignment.toString().split('(')[0],

      // Value properties
      'maxValue': maxValue,
      'minValue': minValue,
      'currentValue': currentValue,

      // Min/max label properties
      'showMinMaxLabels': showMinMaxLabels,

      // Formatting properties
      'prefix': prefix,
      'suffix': suffix,
      'decimalPlaces': decimalPlaces,

      // State properties
      'isDisabled': isDisabled,

      // Gradient properties
      'hasGradient': hasGradient,

      // Indeterminate properties
      'pulseWhenIndeterminate': pulseWhenIndeterminate,

      // Interaction properties
      'isInteractive': isInteractive,

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonProgressConfig': useJsonProgressConfig,
    };
  }

  @override
  State<ProgressBarWidget> createState() => _ProgressBarWidgetState();
}

/// The position of the label relative to the progress bar
enum LabelPosition {
  /// Above the progress bar
  above,

  /// Below the progress bar
  below,

  /// Centered on the progress bar
  center,

  /// To the left of the progress bar
  left,

  /// To the right of the progress bar
  right,
}

class _ProgressBarWidgetState extends State<ProgressBarWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  double _currentProgress = 0.0;
  Timer? _pulseTimer;

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
    _updateProgress();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    if (widget.indeterminate && widget.pulseWhenIndeterminate) {
      _startPulseAnimation();
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(ProgressBarWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.value != oldWidget.value ||
        widget.currentValue != oldWidget.currentValue ||
        widget.maxValue != oldWidget.maxValue ||
        widget.minValue != oldWidget.minValue) {
      _updateProgress();
    }

    if (widget.indeterminate != oldWidget.indeterminate ||
        widget.pulseWhenIndeterminate != oldWidget.pulseWhenIndeterminate) {
      if (widget.indeterminate && widget.pulseWhenIndeterminate) {
        _startPulseAnimation();
      } else {
        _stopPulseAnimation();
      }
    }

    if (widget.animationDuration != oldWidget.animationDuration) {
      _animationController.duration = widget.animationDuration;
    }

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      ),
    );

    _animationController.addListener(() {
      setState(() {});
    });
  }

  void _updateProgress() {
    final double targetProgress;

    if (widget.value != null) {
      targetProgress = widget.value!.clamp(0.0, 1.0);
    } else if (widget.currentValue != null) {
      final range = widget.maxValue - widget.minValue;
      final normalizedValue = (widget.currentValue! - widget.minValue) / range;
      targetProgress = normalizedValue.clamp(0.0, 1.0);
    } else {
      targetProgress = 0.0;
    }

    if (widget.animate && !widget.indeterminate) {
      _animationController.duration = widget.animationDuration;
      _progressAnimation = Tween<double>(
        begin: _currentProgress,
        end: targetProgress,
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: widget.animationCurve,
        ),
      );

      _animationController.forward(from: 0.0);
    } else {
      _currentProgress = targetProgress;
    }
  }

  void _startPulseAnimation() {
    _stopPulseAnimation();
    _pulseTimer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      if (mounted) {
        _animationController.forward(from: 0.0);
      }
    });
    _animationController.forward(from: 0.0);
  }

  void _stopPulseAnimation() {
    _pulseTimer?.cancel();
    _pulseTimer = null;
  }

  String _formatLabel(double value) {
    if (widget.labelFormat != null) {
      return widget.labelFormat!(value);
    }

    final displayValue =
        widget.currentValue ??
        (value * (widget.maxValue - widget.minValue) + widget.minValue);
    final formattedValue = displayValue.toStringAsFixed(widget.decimalPlaces);

    final prefix = widget.prefix ?? '';
    final suffix = widget.suffix ?? '';

    return '$prefix$formattedValue$suffix';
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _stopPulseAnimation();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progress =
        // widget.animate && !widget.indeterminate
        //     ? _progressAnimation.value
        //     :
      widget.value != null ? widget.value!.clamp(0.0, 1.0) : _currentProgress;

    final progressWidget = _buildProgressIndicator(progress);
    final labelWidget = _buildStateSpecificLabel(progress);

    Widget content = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          margin: widget.margin,
          padding: widget.padding,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border:
                widget.hasBorder
                    ? Border.all(
                      color: widget.borderColor,
                      width: widget.borderWidth,
                    )
                    : null,
            boxShadow:
                widget.hasShadow
                    ? [
                      BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.2),
                        blurRadius: widget.elevation,
                        offset: Offset(0, widget.elevation / 2),
                      ),
                    ]
                    : null,
          ),
          child: _buildStateSpecificLayout(
            progressWidget,
            labelWidget,
            progress,
          ),
        ),
      ],
    );

    // Apply opacity for disabled state
    if (widget.isDisabled) {
      content = Opacity(opacity: 1, child: content);
    }

    // Apply tooltip for tooltip state or when showTooltipOnHover is true
    if (widget.progressState == ProgressBarState.tooltip ||
        widget.showTooltipOnHover) {
      content = Tooltip(
        message: widget.tooltipText ?? '${(progress * 100).round()}%',
        child: content,
      );
    }

    // Apply advanced interaction properties
    if (widget.isInteractive) {
      content = GestureDetector(
        onTap:
            widget.isDisabled
                ? null
                : () {
                  // Execute onTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onTap')) {
                    _executeJsonCallback('onTap');
                  }

                  // Call standard callback
                  if (widget.onTap != null) {
                    widget.onTap!();
                  }
                },
        onDoubleTap:
            widget.isDisabled || widget.onDoubleTap == null
                ? null
                : () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                },
        onLongPress:
            widget.isDisabled || widget.onLongPress == null
                ? null
                : () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                },
        child: content,
      );
    }

    // Apply hover detection
    if (widget.onHover != null) {
      content = MouseRegion(
        onEnter: (event) {
          widget.onHover!(true);
        },
        onExit: (event) {
          widget.onHover!(false);
        },
        child: content,
      );
    }

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      final String semanticsValue =
          widget.indeterminate
              ? 'Indeterminate'
              : '${(progress * 100).round()}%';

      content = Semantics(
        label: widget.semanticsLabel,
        value: semanticsValue,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }

  Widget _buildLayout(Widget progressWidget, Widget? labelWidget) {
    if (labelWidget == null) {
      return progressWidget;
    }

    switch (widget.labelPosition) {
      case LabelPosition.above:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            labelWidget,
            const SizedBox(height: 4),
            progressWidget,
            if (widget.showMinMaxLabels) _buildMinMaxLabels(),
          ],
        );
      case LabelPosition.below:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            progressWidget,
            const SizedBox(height: 4),
            // labelWidget,
            if (widget.showMinMaxLabels) _buildMinMaxLabels(),
          ],
        );
      case LabelPosition.center:
        return Stack(
          alignment: Alignment.center,
          children: [
            progressWidget,
            labelWidget,
            if (widget.showMinMaxLabels)
              Positioned(
                bottom: -20,
                left: 0,
                right: 0,
                child: _buildMinMaxLabels(),
              ),
          ],
        );
      case LabelPosition.left:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            labelWidget,
            const SizedBox(width: 8),
            Expanded(child: progressWidget),
            if (widget.showMinMaxLabels) _buildMinMaxLabels(),
          ],
        );
      case LabelPosition.right:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(child: progressWidget),
            const SizedBox(width: 8),
            labelWidget,
            if (widget.showMinMaxLabels) _buildMinMaxLabels(),
          ],
        );
    }
  }

  Widget _buildProgressIndicator(double progress) {
    final effectiveProgress = widget.indeterminate ? null : progress;

    if (widget.type == ProgressBarType.circular) {
      return SizedBox(
        width: widget.size ?? 48.0,
        height: widget.size ?? 48.0,
        child: CircularProgressIndicator(
          value: effectiveProgress,
          backgroundColor: widget.backgroundColor,
          color: _getProgressColor(),
          strokeWidth: widget.thickness,
          semanticsLabel: 'Progress indicator',
          semanticsValue:
              widget.indeterminate
                  ? 'Indeterminate'
                  : '${(progress * 100).round()}%',
        ),
      );
    } else {
      return SizedBox(
        width: widget.width ?? double.infinity,
        height: widget.height ?? widget.thickness,
        child: LinearProgressIndicator(
          value: effectiveProgress,
          backgroundColor: widget.backgroundColor,
          valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor()),
          minHeight: widget.thickness,
          semanticsLabel: 'Progress indicator',
          semanticsValue:
              widget.indeterminate
                  ? 'Indeterminate'
                  : '${(progress * 100).round()}%',
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
      );
    }
  }

  Color _getProgressColor() {
    if (!widget.hasGradient ||
        widget.gradientColors == null ||
        widget.gradientColors!.isEmpty) {
      return _getStateSpecificColor();
    }

    // For simplicity, return the first color when using gradients
    // The actual gradient is applied in the shader in the real implementation
    return widget.gradientColors!.first;
  }

  Widget _buildLabel(double progress) {
    final label = _formatLabel(progress);

    return Text(
      label,
      style:
          widget.labelStyle ??
          // TextStyle(
          //   fontSize: 14,
          //   fontWeight: FontWeight.bold,
          //   color: widget.isDisabled ? Colors.grey : Colors.black87,
          // ),
          FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.bold,
            color: widget.isDisabled ? Colors.grey : Colors.black87,
            fontSize: _getResponsiveValueFontSize(context),
          ),
    );
  }

  Widget _buildMinMaxLabels() {
    final minLabel =
        widget.prefix != null
            ? '${widget.prefix}${widget.minValue}%'
            : '${widget.minValue}%';
    final maxLabel =
        widget.prefix != null
            ? '${widget.prefix}${widget.maxValue}%'
            : '${widget.maxValue}%';

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          minLabel,
          style:
              widget.minMaxLabelStyle ??
              // TextStyle(
              //   fontSize: _getResponsiveFontSize(context),
              //   fontFamily: 'Inter',
              //   color: Colors.black,
              //   //fontWeight: FontWeight.bold,
              // ),
              FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: Color(0xFF333333),
                fontSize: _getResponsiveFontSize(context),
              ),
        ),
        Text(
          maxLabel,
          style:
              widget.minMaxLabelStyle ??
              // TextStyle(
              //   fontSize: _getResponsiveFontSize(context),
              //   fontFamily: 'Inter',
              //   color: Colors.black,
              //   //fontWeight: FontWeight.bold,
              // ),
              FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: Colors.black,
                fontSize: _getResponsiveFontSize(context),
              ),
        ),
      ],
    );
  }

  /// Builds state-specific label based on the progress bar state
  Widget? _buildStateSpecificLabel(double progress) {
    switch (widget.progressState) {
      case ProgressBarState.defaultState:
        return widget.showLabel ? _buildLabel(progress) : null;

      case ProgressBarState.loading:
        return widget.showLabel ? _buildLabel(progress) : null;

      case ProgressBarState.tooltip:
        return widget.showLabel ? _buildLabel(progress) : null;

      case ProgressBarState.indeterminate:
        return widget.showLabel ? _buildLabel(progress) : null;

      case ProgressBarState.finished:
        return widget.showLabel ? _buildLabel(progress) : null;

      case ProgressBarState.withClass:
        // For withClass state, always show percentage labels
        return Text(
          '${(progress * 100).round()}%',
          style:
              widget.labelStyle ??
              // TextStyle(
              //   fontSize: 12,
              //   fontWeight: FontWeight.w500,
              //   color: Colors.grey[700],
              // ),
              FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: Colors.grey[700],
                fontSize: _getResponsiveValueFontSize(context),
              ),
        );
    }
  }

  /// Builds state-specific layout based on the progress bar state
  Widget _buildStateSpecificLayout(
    Widget progressWidget,
    Widget? labelWidget,
    double progress,
  ) {
    switch (widget.progressState) {
      case ProgressBarState.defaultState:
        return _buildLayout(progressWidget, labelWidget);

      case ProgressBarState.loading:
        return _buildLayout(progressWidget, labelWidget);

      case ProgressBarState.tooltip:
        return _buildLayout(progressWidget, labelWidget);

      case ProgressBarState.indeterminate:
        return _buildLayout(progressWidget, labelWidget);

      case ProgressBarState.finished:
        return _buildLayout(progressWidget, labelWidget);

      case ProgressBarState.withClass:
        // For withClass state, show progress bar with min/max labels
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Text(
                  'Progress Bar',
                  // style: TextStyle(
                  //   fontSize: 14,
                  //   fontWeight: FontWeight.w500,
                  //   color: Colors.black87,
                  // ),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: Color(0xFF333333),
                    fontSize: _getResponsiveValueFontSize(context),
                  ),
                ),
                Spacer(),
                if (labelWidget != null) labelWidget,
              ],
            ),
            const SizedBox(height: 8),
            progressWidget,
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '0%',
                  //style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                Text(
                  '100%',
                  //style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        );
    }
  }

  /// Gets the appropriate color based on the progress bar state
  Color _getStateSpecificColor() {
    switch (widget.progressState) {
      case ProgressBarState.defaultState:
        return widget.color;

      case ProgressBarState.loading:
        return Colors.pink[400] ?? Colors.pink;

      case ProgressBarState.tooltip:
        return widget.color;

      case ProgressBarState.indeterminate:
        return widget.color;

      case ProgressBarState.finished:
        return Colors.pink[400] ?? Colors.pink;

      case ProgressBarState.withClass:
        return Colors.pink[400] ?? Colors.pink;
    }
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 14.0; // Large
  } else if (screenWidth >= 1280) {
    return 12.0; // Medium
  } else {
    return 12.0; // Default for very small screens
  }
}
