import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/models/entities_data.dart' as entities_model;
import 'package:nsl/utils/logger.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/text_overflow_detector.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/entity_profile_card.dart'
    as entity_card;
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/mobile_tooltip_utils.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/utils/constants.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/entity_details_panel.dart';
import 'package:nsl/models/conversation_response.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';

class ManualProcessingComponent extends StatefulWidget {
  const ManualProcessingComponent({super.key});

  @override
  State<ManualProcessingComponent> createState() =>
      _ManualProcessingComponentState();
}

class _ManualProcessingComponentState extends State<ManualProcessingComponent> {
  DateTime _lastClickTime = DateTime.now();
  late AccordionController
      _accordionController; // Accordion controller for expansion panels

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
  }

  @override
  void dispose() {
    _accordionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: provider.selectedEntity != null
              ? _buildEntityDetailsView(context, provider)
              : _buildEntityListOnly(context, provider),
        );
      },
    );
  }

  Widget _buildEntityDetailsView(
      BuildContext context, ManualCreationProvider provider) {
    return EntityDetailsPanel(
      entity: provider.selectedEntity!,
      onClose: () => provider.setSelectedEntity(null),
      onBack: () => provider.setSelectedEntity(null),
      chatController: null,
      onSendMessage: null,
      globalEntityElements: const <String, EntityElement>{},
      isInlineMode: true,
    );
  }

  Widget _buildEntityListOnly(
      BuildContext context, ManualCreationProvider provider) {
    // Check if we have entity data
    if (provider.extractedEntityData == null ||
        provider.extractedEntityData!.entityGroups == null ||
        provider.extractedEntityData!.entityGroups!.isEmpty) {
      return _buildEmptyState(context);
    }

    final entityGroups = provider.extractedEntityData!.entityGroups!;

    // Build only the entity list without any header
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
      child: Container(
        padding: EdgeInsets.all(6),
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: entityGroups.length,
          itemBuilder: (context, index) {
            final group = entityGroups[index];
            return Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                color: Colors.white,
              ),
              child: Column(
                children: (group.entities ?? []).map<Widget>((entity) {
                  final List<entities_model.Entity> entityList =
                      group.entities ?? [];
                  return _buildEntityCard(entity, entityList, group);
                }).toList(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(48.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No entity data available',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Switch to Form mode to see extracted entities',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEntityCard(
      entities_model.Entity entity,
      List<entities_model.Entity> allEntities,
      entities_model.EntityGroup group) {
    // If this is a relationship entity, hide it
    if (entity.relationType != null) {
      return SizedBox.shrink();
    }

    final bool isSelected =
        context.read<ManualCreationProvider>().selectedEntity != null &&
            context.read<ManualCreationProvider>().selectedEntity!.id ==
                entity.id;

    // Create a unique global key for this entity card
    final GlobalKey entityCardKey =
        GlobalKey(debugLabel: 'entityCard_${entity.id}');

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              // color: isSelected ? Color(0xFFE3F2FD) : Colors.white,
              // border: Border(
              //   left: isSelected
              //       ? BorderSide(color: Colors.blue.shade700, width: 4)
              //       : BorderSide.none,
              // ),
              ),
          child: Column(
            children: [
              CustomExpansionTileWithEllipsis(
                entity: entity,
                entityCardKey: entityCardKey,
                onExpansionChanged: (expanded) {
                  setState(() {
                    // Update the local entity for immediate UI response
                    entity.expanded = expanded;

                    // Update the entity's expanded state in the global data
                    context
                        .read<ManualCreationProvider>()
                        .extractedEntityData
                        ?.updateEntityExpandedState(entity.id ?? '', expanded);
                  });
                },
                backgroundColor: Colors.transparent,
                onThreeDotsPressed: () {
                  // Handle three dots menu action for entity
                  Logger.info('Three dots pressed for entity: ${entity.title}');
                },
                onTitleTap: () {
                  // Update last click time to prevent tooltip from showing
                  _lastClickTime = DateTime.now();

                  setState(() {
                    final provider = context.read<ManualCreationProvider>();
                    if (provider.selectedEntity != null &&
                        provider.selectedEntity!.id == entity.id) {
                      // Keep selected
                    } else {
                      // Set the selected entity directly - this will trigger the entity details view
                      provider.setSelectedEntity(entity);
                    }
                  });
                },
                showThreeDots: false,
                accordionController:
                    _accordionController, // Pass the accordion controller
                children: [],
              )
            ],
          ),
        ),
      ],
    );
  }
}

// Shared utility class for bottom sheet functionality
class EntityMenuItems {
  static const List<Map<String, String>> _menuItems = [
    {
      'icon': 'assets/images/entity/nested.svg',
      'title': 'Nested',
      'value': 'nested'
    },
    {
      'icon': 'assets/images/entity/shared.svg',
      'title': 'Shared',
      'value': 'shared'
    },
    {
      'icon': 'assets/images/entity/junction.svg',
      'title': 'Junction',
      'value': 'junction'
    },
    {
      'icon': 'assets/images/entity/agent.svg',
      'title': 'Agents',
      'value': 'agents'
    },
    {
      'icon': 'assets/images/entity/workflow.svg',
      'title': 'Workflows',
      'value': 'workflows'
    },
  ];

  static void show(BuildContext context, {Function(String)? onItemSelected}) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Menu items
              ..._menuItems.map((item) => _buildBottomSheetItem(
                    context: context,
                    icon: item['icon']!,
                    title: item['title']!,
                    onTap: () {
                      Navigator.pop(context);
                      onItemSelected?.call(item['value']!);
                    },
                  )),
            ],
          ),
        );
      },
    );
  }

  static Widget _buildBottomSheetItem({
    required BuildContext context,
    required String icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Row(
          children: [
            SvgPicture.asset(
              icon,
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(
                Colors.grey.shade700,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                color: Colors.black,
                fontWeight: FontManager.medium,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Utility class for responsive behavior
class ScreenResponsiveHelper {
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.mobileBreakpoint;
  }
}

class CustomExpansionTileNew extends StatefulWidget {
  final Widget title;
  final List<Widget> children;
  final bool initiallyExpanded;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final VoidCallback? onThreeDotsPressed;
  final Color backgroundColor;
  final bool showArrow;
  final bool showThreeDots;
  final String? panelId; // Unique identifier for accordion behavior
  final AccordionController?
      accordionController; // Controller for accordion behavior

  const CustomExpansionTileNew({
    super.key,
    required this.title,
    required this.children,
    this.initiallyExpanded = false,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.onThreeDotsPressed,
    this.backgroundColor = Colors.white,
    this.showArrow = true,
    this.showThreeDots = true,
    this.panelId,
    this.accordionController,
  });

  @override
  State<CustomExpansionTileNew> createState() => CustomExpansionTileNewState();
}

class CustomExpansionTileNewState extends State<CustomExpansionTileNew>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeIn));

    // Set initial expansion state
    if (widget.accordionController != null && widget.panelId != null) {
      _isExpanded =
          widget.accordionController!.isPanelExpanded(widget.panelId!);
    } else {
      _isExpanded = widget.initiallyExpanded;
    }

    if (_isExpanded) {
      _controller.value = 1.0;
    }

    // Listen to accordion controller changes
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.addListener(_onAccordionStateChanged);
    }
  }

  @override
  void didUpdateWidget(CustomExpansionTileNew oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initiallyExpanded != oldWidget.initiallyExpanded) {
      if (widget.initiallyExpanded) {
        _isExpanded = true;
        _controller.forward();
      } else {
        _isExpanded = false;
        _controller.reverse();
      }
    }
  }

  void _onAccordionStateChanged() {
    if (widget.accordionController != null && widget.panelId != null) {
      final shouldBeExpanded =
          widget.accordionController!.isPanelExpanded(widget.panelId!);
      if (shouldBeExpanded != _isExpanded) {
        setState(() {
          _isExpanded = shouldBeExpanded;
          if (_isExpanded) {
            _controller.forward();
          } else {
            _controller.reverse();
          }
          widget.onExpansionChanged(_isExpanded);
        });
      }
    }
  }

  @override
  void dispose() {
    // Remove accordion controller listener
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.removeListener(_onAccordionStateChanged);
    }
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    // Only allow expansion if arrow is shown
    if (!widget.showArrow) return;

    // If accordion controller is provided and panelId is set, use accordion behavior
    if (widget.accordionController != null && widget.panelId != null) {
      widget.accordionController!.togglePanel(widget.panelId!);
      return;
    }

    // Default behavior (non-accordion)
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onExpansionChanged(_isExpanded);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: _isExpanded ? Color(0xffE4EDFF) : widget.backgroundColor,
      child: Column(
        children: [
          // Title row with conditional tap handlers
          InkWell(
            onTap: () {
              // Apply mobile behavior for both mobile and desktop
              if (!_isExpanded && widget.showArrow) {
                // First tap: expand the content
                _toggleExpansion();
              } else if (_isExpanded) {
                // Second tap on expanded content: call onTitleTap (which should show bottom sheet)
                widget.onTitleTap();
              } else {
                // If no arrow, just call onTitleTap
                widget.onTitleTap();
              }
            },
            child: Container(
              color: Colors
                  .transparent, // Make it transparent to let parent color show through
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.xs,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                // crossAxisAlignment: _isExpanded
                //     ? CrossAxisAlignment.start
                //     : CrossAxisAlignment.center,
                children: [
                  Expanded(child: widget.title),
                  // Conditionally show bell icon
                  if (widget.showArrow)
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: InkWell(
                        onTap: () {
                          // Bell icon click action
                          _toggleExpansion();
                        },
                        child: Icon(
                          Icons.notifications_none,
                          size: 20,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  // Static toggle menu after down arrow
                  if (widget.showArrow) StaticToggleMenu(),
                  // Always show three dots icon
                  widget.showThreeDots
                      ? HoverThreeDotsIcon(
                          onTap: widget.onThreeDotsPressed ?? () {},
                        )
                      : SizedBox(),
                ],
              ),
            ),
          ),
          // Expandable content
          AnimatedBuilder(
            animation: _controller.view,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  heightFactor: _heightFactor.value,
                  alignment:
                      Alignment.topLeft, // Align content to the start (left)
                  child: child,
                ),
              );
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widget.children,
            ),
          ),
        ],
      ),
    );
  }
}

class HoverThreeDotsIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverThreeDotsIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverThreeDotsIcon> createState() => _HoverThreeDotsIconState();
}

class _HoverThreeDotsIconState extends State<HoverThreeDotsIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    final isMobile = ScreenResponsiveHelper.isMobile(context);

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: () {
          if (isMobile) {
            EntityMenuItems.show(context);
          } else {
            widget.onTap();
          }
        },
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            Icons.more_vert,
            size: 20,
            color: isHovered ? const Color(0xff0058FF) : Colors.grey.shade800,
          ),
        ),
      ),
    );
  }
}

// Provider for managing toggle menu state
class ToggleMenuProvider extends ChangeNotifier {
  bool _isHovered = false;
  bool _isMenuOpen = false;

  bool get isHovered => _isHovered;
  bool get isMenuOpen => _isMenuOpen;

  void setHovered(bool hovered) {
    if (_isHovered != hovered) {
      _isHovered = hovered;
      notifyListeners();
    }
  }

  void setMenuOpen(bool open) {
    if (_isMenuOpen != open) {
      _isMenuOpen = open;
      notifyListeners();
    }
  }

  void handleMenuSelection(String value) {
    // Handle menu selection logic here
    // This can be extended to handle different menu actions
    setMenuOpen(false);
    notifyListeners();
  }
}

class StaticToggleMenu extends StatelessWidget {
  const StaticToggleMenu({super.key});

  @override
  Widget build(BuildContext context) {
    final isMobile = ScreenResponsiveHelper.isMobile(context);

    if (isMobile) {
      // Mobile: Show simple icon that opens bottom sheet
      return InkWell(
        onTap: () => EntityMenuItems.show(context),
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
          height: 28,
          width: 28,
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(6.0),
          ),
          child: const Center(
            child: Icon(
              Icons.more_vert,
              size: 20,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    // Desktop: Show original popup menu
    return ChangeNotifierProvider(
      create: (_) => ToggleMenuProvider(),
      child: Consumer<ToggleMenuProvider>(
        builder: (context, provider, child) {
          return MouseRegion(
            onEnter: (_) => provider.setHovered(true),
            onExit: (_) => provider.setHovered(false),
            child: InkWell(
              onTap: () {},
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              splashFactory: NoSplash.splashFactory,
              child: Center(
                child: Container(
                  height: 28,
                  width: 28,
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: provider.isMenuOpen
                        ? const Color(0xff0058FF)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Center(
                    child: _buildDesktopPopupMenu(provider),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDesktopPopupMenu(ToggleMenuProvider provider) {
    return PopupMenuButton<String>(
      tooltip: '',
      icon: Icon(
        Icons.more_vert,
        size: 20,
        color: provider.isMenuOpen
            ? Colors.white
            : provider.isHovered
                ? const Color(0xff0058FF)
                : Colors.grey,
      ),
      onSelected: (String value) {
        provider.handleMenuSelection(value);
      },
      onOpened: () {
        provider.setMenuOpen(true);
      },
      onCanceled: () {
        provider.setMenuOpen(false);
      },
      constraints: const BoxConstraints(
        minWidth: 100,
        maxWidth: 120,
      ),
      itemBuilder: (BuildContext context) => EntityMenuItems._menuItems
          .map((item) => PopupMenuItem<String>(
                value: item['value'],
                height: 28,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      item['icon']!,
                      width: 16,
                      height: 16,
                      colorFilter: ColorFilter.mode(
                        Colors.grey.shade700,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        item['title']!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          color: Colors.black,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
      offset: const Offset(0, 35),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
        side: const BorderSide(
          color: Color(0xFF0058FF),
          width: 0.5,
        ),
      ),
      elevation: 8,
      color: Colors.white,
      splashRadius: 20,
      padding: const EdgeInsets.symmetric(vertical: 4),
    );
  }
}

class CustomExpansionTileWithEllipsis extends StatefulWidget {
  final entities_model.Entity entity;
  final GlobalKey entityCardKey;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final VoidCallback? onThreeDotsPressed;
  final Color backgroundColor;
  final List<Widget> children;
  final bool? showThreeDots;
  final AccordionController?
      accordionController; // Controller for accordion behavior

  const CustomExpansionTileWithEllipsis({
    super.key,
    required this.entity,
    required this.entityCardKey,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.onThreeDotsPressed,
    this.backgroundColor = Colors.transparent,
    this.children = const [],
    this.showThreeDots,
    this.accordionController,
  });

  @override
  State<CustomExpansionTileWithEllipsis> createState() =>
      CustomExpansionTileWithEllipsisState();
}

class CustomExpansionTileWithEllipsisState
    extends State<CustomExpansionTileWithEllipsis> {
  late bool isExpanded;

  // Add overlay management for entity hover cards
  OverlayEntry? _profileTooltipOverlay;
  DateTime _lastClickTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    isExpanded = widget.entity.expanded ?? false;
  }

  @override
  void didUpdateWidget(CustomExpansionTileWithEllipsis oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.entity.expanded != widget.entity.expanded) {
      isExpanded = widget.entity.expanded ?? false;
    }
  }

  @override
  void dispose() {
    _hideEntityProfileTooltip(); // Clean up any active tooltips
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Log the expansion state for debugging
    Logger.info(
        "Building CustomExpansionTile with expanded: $isExpanded for entity: ${widget.entity.title}");

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available width for text (accounting for padding, potential arrow space, and three dots space)
        final double availableWidth = constraints.maxWidth -
            (AppSpacing.xs * 2) -
            60; // 30px for arrow + 30px for three dots

        // Check if text would overflow using simplified approach
        final bool wouldOverflow = _wouldTextOverflow(availableWidth);

        Logger.info(
            "Text overflow check for ${widget.entity.title}: wouldOverflow=$wouldOverflow, availableWidth=$availableWidth");

        return CustomExpansionTileNew(
          title: Builder(
            builder: (context) {
              // Show with ellipsis when collapsed, without ellipsis when expanded
              return _buildEntityTitle(
                widget.entityCardKey,
                widget.entity,
                isExpanded,
              );
            },
          ),
          initiallyExpanded: isExpanded,
          onExpansionChanged: (expanded) {
            setState(() {
              isExpanded = expanded;
            });
            widget.onExpansionChanged(expanded);
          },
          onTitleTap: widget.onTitleTap,
          onThreeDotsPressed: widget.onThreeDotsPressed,
          backgroundColor: widget.backgroundColor,
          showArrow: wouldOverflow, // Only show arrow if text would overflow
          showThreeDots: widget.showThreeDots ?? true,
          panelId: widget.entity.id, // Use entity ID as panel ID
          accordionController: widget.accordionController,
          children: widget.children,
        );
      },
    );
  }

  // Check if the text would overflow in the available width (simplified approach)
  bool _wouldTextOverflow(double availableWidth) {
    if (availableWidth <= 0) return false;

    // Create a simplified text string for measurement (without WidgetSpans)
    String fullText = widget.entity.title ?? 'Untitled';
    if (widget.entity.attributeString != null &&
        widget.entity.attributeString!.isNotEmpty) {
      fullText += ' has ${widget.entity.attributeString}';
    }

    // Use simple text overflow detection
    return TextOverflowDetector.wouldTextOverflow(
      text: fullText,
      style: FontManager.getCustomStyle(
        fontWeight: FontManager.semiBold,
        fontSize: ResponsiveFontSizes.bodyMedium(context),
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
      maxWidth: availableWidth,
      maxLines: 1,
    );
  }

  // Build entity title with or without ellipsis
  Widget _buildEntityTitle(
      GlobalKey entityCardKey, entities_model.Entity entity, bool expanded) {
    return Row(
      key: entityCardKey,
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.fromLTRB(0, AppSpacing.xs, 0, AppSpacing.md),
            child: Row(
              children: [
                // Create a combined RichText for the entire title with attributes
                Expanded(
                  child: Builder(
                    builder: (context) {
                      final GlobalKey titleKey =
                          GlobalKey(debugLabel: 'entityTitle_${entity.id}');

                      // Create a list of text spans for the combined title
                      List<InlineSpan> titleSpans = [];

                      // Add the entity title with bold style
                      TextSpan titleSpan = TextSpan(
                        text: entity.title ?? 'Untitled',
                        style: TextStyle(
                          fontWeight: FontManager.semiBold,
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      );

                      // Wrap title with responsive tooltip (mobile + web)
                      titleSpans.add(WidgetSpan(
                        child: _buildEntityTitleWithTooltip(
                          context,
                          titleSpan,
                          entity,
                        ),
                      ));

                      // Add attributes if they exist
                      if (entity.attributeString != null &&
                          entity.attributeString!.isNotEmpty) {
                        // Add " has " text
                        titleSpans.add(
                          TextSpan(
                            text: ' has ',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontManager.medium,
                              color: Colors.grey.shade700,
                              fontFamily: FontManager.fontFamilyTiemposText,
                            ),
                          ),
                        );

                        // Add attribute spans (simplified to avoid WidgetSpan issues)
                        titleSpans.addAll(_buildSimplifiedAttributeSpans(
                          entity.attributeString ?? '',
                          entity.attributes ?? [],
                        ));
                      }

                      // Log the RichText properties for debugging
                      Logger.info(
                          "RichText with expanded: $expanded, overflow: ${!expanded ? 'ellipsis' : 'visible'}, maxLines: ${!expanded ? 1 : 'null'}");

                      return RichText(
                        key: titleKey,
                        text: TextSpan(
                            children: titleSpans,
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontManager.medium,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              height: 2,
                              textBaseline: TextBaseline.alphabetic,
                            )),
                        overflow: !expanded
                            ? TextOverflow.ellipsis
                            : TextOverflow.visible,
                        softWrap: expanded,
                        maxLines: !expanded ? 1 : null,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build simplified attribute spans (avoiding WidgetSpan for PK indicators in overflow detection)
  List<InlineSpan> _buildSimplifiedAttributeSpans(
      String attributeString, List<entities_model.Attribute> attributes) {
    // Log for debugging
    Logger.info("Building simplified attribute spans for: $attributeString");

    if (attributeString.isEmpty) {
      return [];
    }

    // Create a map of attribute names to their isPk status
    final Map<String, bool> primaryKeyMap = {};
    for (var attribute in attributes) {
      if (attribute.name != null) {
        primaryKeyMap[attribute.name!] = attribute.isPk ?? false;
      }
    }

    final Map<String, bool> foreignKeyMap = {};
    for (var attribute in attributes) {
      if (attribute.name != null) {
        foreignKeyMap[attribute.name!] = attribute.isFk ?? false;
      }
    }

    // Split the attribute string by commas and "and"
    List<String> parts = [];
    final andParts = attributeString.split(' and ');
    for (int i = 0; i < andParts.length; i++) {
      if (i < andParts.length - 1) {
        final commaParts = andParts[i].split(', ');
        parts.addAll(commaParts);
      } else {
        parts.add(andParts[i]);
      }
    }

    // Create a list of text spans for each attribute
    List<InlineSpan> textSpans = [];
    for (int i = 0; i < parts.length; i++) {
      String part = parts[i].trim();
      bool isPrimaryKey = false;
      bool isForeignKey = false;

      for (var attrName in primaryKeyMap.keys) {
        if (part.contains(attrName) && primaryKeyMap[attrName] == true) {
          isPrimaryKey = true;
          break;
        }
      }

      for (var attrName in foreignKeyMap.keys) {
        if (part.contains(attrName) && foreignKeyMap[attrName] == true) {
          isForeignKey = true;
          break;
        }
      }

      // Add the attribute with or without PK indicator
      if (isPrimaryKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: FontManager.getCustomStyle(
              fontWeight: FontManager.semiBold,
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        );
        textSpans.add(TextSpan(text: ' '));
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5),
              child: Text(
                '^PK',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontManager.semiBold,
                  color: Color(0xff0058FF),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        );
      } else if (isForeignKey) {
        textSpans.add(
          TextSpan(
            text: part,
            style: FontManager.getCustomStyle(
              fontWeight: FontManager.semiBold,
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        );
        textSpans.add(TextSpan(text: ' '));
        textSpans.add(
          WidgetSpan(
            alignment: PlaceholderAlignment.top,
            child: Transform.translate(
              offset: Offset(0, -5),
              child: Text(
                '^FK',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontManager.semiBold,
                  color: Color(0xff0058FF),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        );
      } else {
        textSpans.add(
          TextSpan(
            text: part,
            style: FontManager.getCustomStyle(
              fontWeight: FontManager.semiBold,
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        );
      }

      // Add separator if not the last item
      if (i < parts.length - 1) {
        if (i == parts.length - 2) {
          textSpans.add(TextSpan(
            text: ' and ',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontManager.medium,
              color: Colors.grey.shade700,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ));
        } else {
          textSpans.add(TextSpan(
            text: ', ',
            style: FontManager.getCustomStyle(
              color: Colors.black,
            ),
          ));
        }
      }
    }

    return textSpans;
  }

  /// Builds entity title with responsive tooltip (mobile long press + web hover)
  Widget _buildEntityTitleWithTooltip(
    BuildContext context,
    TextSpan titleSpan,
    entities_model.Entity entity,
  ) {
    final tooltipContent = entity_card.EntityProfileCard(
      entity: entity,
      width: MobileTooltipUtils.isMobile(context) ? double.infinity : 320.0,
      leftMargin: MobileTooltipUtils.isMobile(context) ? 0 : 0,
    );

    final titleText = Text.rich(
      style: TextStyle(
        fontSize: ResponsiveFontSizes.bodyMedium(context),
        fontFamily: FontManager.fontFamilyTiemposText,
        height: 1.3,
        textBaseline: TextBaseline.alphabetic,
      ),
      TextSpan(text: titleSpan.text, style: titleSpan.style),
    );

    return MobileTooltipUtils.createResponsiveTooltipWrapper(
      context: context,
      child: titleText,
      tooltipContent: tooltipContent,
      webTooltipChild: Builder(
        builder: (context) {
          final titleKey = GlobalKey();
          return MouseRegion(
            key: titleKey,
            cursor: SystemMouseCursors.click,
            onEnter: (_) {
              _showEntityProfileTooltip(titleKey, entity);
            },
            onExit: (_) {
              _hideEntityProfileTooltip();
            },
            child: titleText,
          );
        },
      ),
    );
  }

  // Show entity profile tooltip
  void _showEntityProfileTooltip(
      GlobalKey titleKey, entities_model.Entity entity) {
    // Don't show tooltip if the user is clicking
    if (DateTime.now().difference(_lastClickTime).inMilliseconds < 300) {
      return;
    }

    // Hide any existing tooltips first
    _hideEntityProfileTooltip();

    // Get the position of the entity title
    final RenderBox? renderBox =
        titleKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final screenSize = MediaQuery.of(context).size;

    // Calculate tooltip dimensions
    const double tooltipWidth = 320.0;
    const double tooltipMaxHeight = 300.0;

    // Calculate position to ensure tooltip stays within screen bounds
    double left = position.dx;
    double top =
        position.dy + 30; // Position below the entity title with some spacing

    // Adjust horizontal position if tooltip would go off-screen
    if (left + tooltipWidth > screenSize.width - 20) {
      left = screenSize.width - tooltipWidth - 20;
    }
    if (left < 20) {
      left = 20;
    }

    // Adjust vertical position if tooltip would go off-screen
    if (top + tooltipMaxHeight > screenSize.height - 20) {
      top = position.dy -
          tooltipMaxHeight -
          10; // Position above the entity title
    }
    if (top < 20) {
      top = 20;
    }

    // Create the overlay entry
    _profileTooltipOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: left,
        top: top,
        child: Material(
          color: Colors.transparent,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: tooltipWidth,
              maxHeight: tooltipMaxHeight,
            ),
            child: entity_card.EntityProfileCard(
              entity: entity,
              width: tooltipWidth,
              leftMargin: 0, // Remove the default left margin for tooltips
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the screen
    Overlay.of(context).insert(_profileTooltipOverlay!);
  }

  // Hide entity profile tooltip
  void _hideEntityProfileTooltip() {
    _profileTooltipOverlay?.remove();
    _profileTooltipOverlay = null;
    _lastClickTime = DateTime.now();
  }
}
