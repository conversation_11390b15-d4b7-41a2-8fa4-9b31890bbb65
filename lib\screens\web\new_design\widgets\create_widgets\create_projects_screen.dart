import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/api_responses/create_get_projects_model.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/response_text_parser.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

// ignore: must_be_immutable
class CreateProjectScreen extends StatefulWidget {
  CreateProjectScreen({super.key, this.showNavigationBar = true});

  bool showNavigationBar = true;

  @override
  State<CreateProjectScreen> createState() => CreateProjectScreenState();
}

class CreateProjectScreenState extends State<CreateProjectScreen>
    with TickerProviderStateMixin {
  List<Project> projects = [];
  List<Project> _allProjects = []; // Store original unfiltered projects
  List<Project> _filteredProjects = []; // Store filtered projects for display
  bool isLoading = true;

  // Search state
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // Pagination state
  int _currentPage = 1;
  int _itemsPerPage = 10;
  int _totalPages = 1;

  // Create project modal state
  bool _showCreateProjectModal = false;
  final TextEditingController _projectNameController = TextEditingController();
  String? _selectedIndustry;
  bool _isDropdownFocused = false;
  bool isLoadingChatSession = false;

  final List<String> _industries = [
    'E-Commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _loadProjects();
    });

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future _loadProjects() async {
    try {
      // Parse the JSON string
      // final data = json.decode(projectsJsonString);
      final provider =
          Provider.of<WebHomeProviderStatic>(context, listen: false);
      await provider.fetchCreateChatHistory();
      final data = provider.chatHistory;

      // Convert to Project objects
      List<Project> loadedProjects = (data as List)
          .map((projectJson) => Project.fromJson(projectJson))
          .toList();
      loadedProjects = loadedProjects.reversed.toList();
      setState(() {
        _allProjects = loadedProjects;
        projects = loadedProjects;
        _filteredProjects = loadedProjects;
        isLoading = false;
        _updatePagination();
      });
    } catch (e) {
      setState(() {
        _allProjects = [];
        projects = [];
        _filteredProjects = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading projects: $e');
    }
  }

  // Handle search text changes
  void _onSearchChanged() {
    _filterProjects();
  }

  // Filter projects based on search text
  void _filterProjects() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredProjects = List.from(_allProjects);
      } else {
        _filteredProjects = _allProjects
            .where((project) =>
                (project.projectName ?? '')
                    .toLowerCase()
                    .contains(searchText) ||
                (project.status ?? '').toLowerCase().contains(searchText))
            .toList();
      }

      projects = _filteredProjects;
      // Reset to first page when filtering
      _currentPage = 1;
      _updatePagination();
    });
  }

  // Update pagination based on current state
  void _updatePagination() {
    if (projects.isEmpty) {
      _totalPages = 1;
      return;
    }

    _totalPages = (projects.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentPage > _totalPages) {
      _currentPage = _totalPages;
    }
    if (_currentPage < 1) {
      _currentPage = 1;
    }
  }

  // Get projects for current page
  List<Project> _getCurrentPageProjects() {
    if (projects.isEmpty) return [];

    int startIndex = (_currentPage - 1) * _itemsPerPage;
    int endIndex = startIndex + _itemsPerPage;

    if (startIndex >= projects.length) return [];
    if (endIndex > projects.length) endIndex = projects.length;

    return projects.sublist(startIndex, endIndex);
  }

  // Navigate to previous page
  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  // Navigate to next page
  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xffF7F9FB) : Colors.transparent,
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              widget.showNavigationBar
                  ?
                  // Top navigation bar
                  Padding(
                      padding: widget.showNavigationBar
                          ? const EdgeInsets.only(
                              left: 94, right: 94, bottom: 0.0, top: 0)
                          : EdgeInsets.zero,
                      child: Column(
                        children: [
                          const HoverNavItems(),
                          SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Project text
                              Expanded(
                                child: Text(
                                  'Project',
                                  style: FontManager.getCustomStyle(
                                    fontSize: ResponsiveFontSizes.titleMedium(
                                        context),
                                    fontWeight: FontWeight.w600,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black87,
                                  ),
                                ),
                              ),

                              // Search bar with filter
                              Expanded(
                                child: Container(
                                  width:
                                      MediaQuery.of(context).size.width / 3.722,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6),
                                    border:
                                        Border.all(color: Colors.grey.shade200),
                                  ),
                                  child: Row(
                                    children: [
                                      // Search text field
                                      Expanded(
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(left: 16.0),
                                          child: TextField(
                                            controller: _searchController,
                                            focusNode: _searchFocusNode,
                                            cursorHeight: 16,
                                            decoration: InputDecoration(
                                              enabledBorder: InputBorder.none,
                                              focusedBorder: InputBorder.none,
                                              hintText: 'Search Projects...',
                                              border: InputBorder.none,
                                              hintStyle:
                                                  FontManager.getCustomStyle(
                                                      fontSize:
                                                          ResponsiveFontSizes
                                                              .bodyMedium(
                                                                  context),
                                                      color: Color(0xff939393)),
                                              isDense: true,
                                              contentPadding: EdgeInsets.zero,
                                              filled: false,
                                              fillColor: Colors.transparent,
                                            ),
                                          ),
                                        ),
                                      ), // Search icon
                                      _HoverSvgButton(
                                        normalIconPath:
                                            'assets/images/search.svg',
                                        hoverIconPath:
                                            'assets/images/search.svg',
                                        onPressed: () {
                                          // Focus the text field and position cursor at the end
                                          _searchFocusNode.requestFocus();
                                          // Set cursor position to the end of the text
                                          _searchController.selection =
                                              TextSelection.fromPosition(
                                                  TextPosition(
                                                      offset: _searchController
                                                          .text.length));
                                          _filterProjects();
                                        },
                                        imageWidth: 20,
                                        imageHeight: 20,
                                        showBorderOnHover: false,
                                      ),
                                      // Divider between search and filter
                                      Container(
                                        height: 23,
                                        width: 1,
                                        color: Colors.grey.shade200,
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                      ),
                                      // Filter icon
                                      _HoverSvgButton(
                                        normalIconPath:
                                            'assets/images/filter-icon.svg',
                                        hoverIconPath:
                                            'assets/images/filter-hover.svg',
                                        onPressed: () {},
                                        imageWidth: 32,
                                        imageHeight: 32,
                                        showBorderOnHover: false,
                                      ),
                                      const SizedBox(width: 8),
                                    ],
                                  ),
                                ),
                              ),

                              // Create Project button
                              Expanded(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    HoverCreateButton(
                                      text: 'Create Project',
                                      onPressed: () {
                                        // setState(() {
                                        //   _showCreateProjectModal = true;
                                        // });
                                        Provider.of<WebHomeProviderStatic>(
                                                context,
                                                listen: false)
                                            .currentCreateScreenIndex = 1;
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                        ],
                      ),
                    )
                  : Container(),
              // Main content
              myProjects(context),
            ],
          ),
          // Create Project Modal
          if (_showCreateProjectModal) _buildCreateProjectModal(),
        ],
      ),
    );
  }

  Widget myProjects(context) {
    return Expanded(
      child: SingleChildScrollView(
        physics: widget.showNavigationBar
            ? const AlwaysScrollableScrollPhysics()
            : const NeverScrollableScrollPhysics(),
        child: Container(
          padding:
              const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 32),
              // Projects table
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : projects.isEmpty
                      ? const Center(child: Text('No projects found'))
                      : _buildProjectsTable(),
              // Pagination controls
              if (projects.isNotEmpty && _totalPages > 1)
                Container(
                  margin: const EdgeInsets.only(top: 20, bottom: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Page info
                      Text(
                        'Page $_currentPage of $_totalPages',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodySmall(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey,
                        ),
                      ),
                      // Navigation buttons
                      Row(
                        children: [
                          // Previous button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_left, size: 20),
                            onPressed:
                                _currentPage > 1 ? _goToPreviousPage : null,
                          ),
                          const SizedBox(width: 8),
                          // Next button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_right, size: 20),
                            onPressed: _currentPage < _totalPages
                                ? _goToNextPage
                                : null,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectsTable() {
    List<Project> currentPageProjects = _getCurrentPageProjects();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        // border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Color(0xFFF7F9FB),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'Project Name',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Created on',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Last Modified',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Last Modified by',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Status',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Actions',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Table rows
          ...currentPageProjects.asMap().entries.map((entry) {
            int index = entry.key;
            Project project = entry.value;
            bool isEvenRow = index % 2 == 0;

            return GestureDetector(
              onTap: () {
                final provider =
                    Provider.of<WebHomeProviderStatic>(context, listen: false);
                final projectId = project.projectId;

                TextEditingController chatController = provider.chatController;
                if (projectId != null) {
                  setState(() {
                    isLoadingChatSession = true;
                    provider.clearMessages();
                  });

                  provider
                      .fetchConversationFromNewAPI(projectId)
                      .then((result) {
                    if (result['success']) {
                      final conversations =
                          result['data']['conversations'] as List<dynamic>?;

                      if (conversations != null && conversations.isNotEmpty) {
                        // Get the last conversation from the array
                        final lastConversation = conversations.last;
                        final conversationId =
                            lastConversation['conversation_id'] as String?;

                        if (conversationId != null) {
                          // Now fetch the messages for this conversation
                          provider
                              .fetchConversationMessages(conversationId)
                              .then((messagesResult) {
                            if (messagesResult['success']) {
                              final messages = messagesResult['data']
                                  ['messages'] as List<dynamic>?;

                              provider.clearMessages();

                              if (messages != null && messages.isNotEmpty) {
                                for (var messageItem in messages) {
                                  if (messageItem['type']
                                          .toString()
                                          .toLowerCase() ==
                                      'user') {
                                    provider.addMessage(ChatMessage(
                                      content: messageItem['content'] ?? '',
                                      isUser: true,
                                    ));
                                  }

                                  if (messageItem['type']
                                          .toString()
                                          .toLowerCase() ==
                                      'ai') {
                                    final responseText =
                                        messageItem['content'] ??
                                            'No response provided';
                                    String thinkingMessage = ResponseTextParser
                                        .extractThinkingSectionAsString(
                                            responseText);
                                    List<String> thinkingList = [];
                                    if (thinkingMessage.isNotEmpty) {
                                      thinkingList.add(thinkingMessage);
                                    }
                                    provider.lastSolutionMessage = responseText;
                                    // provider.lastSolutionResponse = response['data'];
                                    ChatMessage message = ChatMessage(
                                      content: responseText,
                                      isUser: false,
                                      reasoningData: thinkingList,
                                      isReasoningDataExpanded: false,
                                      customContentItem: 1,
                                      isTypingComplete: true,
                                      hasNSLTypingCompleted: true,
                                      extraFunction:
                                          (option, isChecked, multiSelect) {
                                        if (multiSelect) {
                                          if (isChecked) {
                                            if (!chatController.text
                                                .contains(option)) {
                                              chatController.text += option;
                                            }
                                          } else {
                                            chatController.text = chatController
                                                .text
                                                .replaceAll(option, '');
                                          }
                                        } else {
                                          if (isChecked) {
                                            if (!chatController.text
                                                .contains(option)) {
                                              chatController.text = option;
                                            }
                                          } else {
                                            chatController.text = chatController
                                                .text
                                                .replaceAll(option, '');
                                          }
                                        }
                                      },
                                    );

                                    provider.addMessage(message);
                                    // provider.addMessage(ChatMessage(
                                    //   content: messageItem['content'] ?? '',
                                    //   isUser: false,
                                    //   isTypingComplete: true,
                                    // ));
                                  }
                                }

                                // provider.setModeSessionForContinuation(conversationId, extractedMode ?? 'nsl_expert');
                              } else {
                                provider.addMessage(ChatMessage(
                                  content:
                                      'No messages available for ${project.projectName ?? 'Untitled'}',
                                  isUser: false,
                                ));

                                // provider.setModeSessionForContinuation(conversationId, extractedMode ?? 'nsl_expert');

                                // final modeName = provider.getModeNameById(extractedMode ?? 'nsl_expert');
                                // if (modeName != null) {
                                //   provider.selectedQuickMessage = modeName;
                                // }
                              }
                            } else {
                              isLoadingChatSession = false;
                              provider.addMessage(ChatMessage(
                                content:
                                    'Error loading messages: ${messagesResult['message']}',
                                isUser: false,
                              ));
                            }
                          }).catchError((error) {
                            isLoadingChatSession = false;
                            provider.addMessage(ChatMessage(
                              content: 'Error: $error',
                              isUser: false,
                            ));
                          });
                        } else {
                          isLoadingChatSession = false;
                          provider.addMessage(ChatMessage(
                            content:
                                'No conversation ID found for ${project.projectName ?? 'Untitled'}',
                            isUser: false,
                          ));
                        }
                      } else {
                        isLoadingChatSession = false;
                        provider.addMessage(ChatMessage(
                          content:
                              'No conversations available for ${project.projectName ?? 'Untitled'}',
                          isUser: false,
                        ));
                      }
                    } else {
                      isLoadingChatSession = false;
                      provider.addMessage(ChatMessage(
                        content:
                            'Error loading conversation: ${result['message']}',
                        isUser: false,
                      ));
                    }
                  }).catchError((error) {
                    isLoadingChatSession = false;
                    provider.addMessage(ChatMessage(
                      content: 'Error: $error',
                      isUser: false,
                    ));
                  });
                }
                provider.currentCreateScreenIndex = 2;
              },
              child: _HoverTableRow(
                project: project,
                isEvenRow: isEvenRow,
                onEditPressed: () {
                  // Handle edit action here
                  print('Edit pressed for project: ${project.projectName}');
                  // You can add your edit logic here
                },
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCreateProjectModal() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showCreateProjectModal = false;
        });
      },
      child: Container(
        color: Colors.black87.withOpacity(0.5),
        child: Center(
          child: GestureDetector(
            onTap: () {}, // Prevent closing when tapping on the modal content
            child: Container(
              width: 400,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black87.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      'Create A Project',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.headlineSmall(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Name field
                  Text(
                    'Name',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 40,
                    child: TextField(
                      controller: _projectNameController,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide:
                              BorderSide(color: Color(0xff0058FF), width: 1),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                        hintText: 'Enter project name',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Industry field
                  Text(
                    'Industry',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Focus(
                    onFocusChange: (hasFocus) {
                      setState(() {
                        _isDropdownFocused = hasFocus;
                      });
                    },
                    child: Container(
                      width: double.infinity,
                      height: 40,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _isDropdownFocused
                              ? Color(0xff0058FF)
                              : Colors.grey.shade300,
                          width: _isDropdownFocused ? 1 : 1,
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedIndustry,
                          hint: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Text(
                              'Select industry',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ),
                          icon: Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          isExpanded: true,
                          items: _industries.map((String industry) {
                            return DropdownMenuItem<String>(
                              value: industry,
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 12),
                                child: Text(
                                  industry,
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodyMedium(context),
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            setState(() {
                              _selectedIndustry = newValue;
                            });
                          },
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Start button
                  Center(
                    child: SizedBox(
                      width: 200,
                      height: 44,
                      child: ElevatedButton(
                        onPressed: () {
                          // Handle project creation
                          if (_projectNameController.text.isNotEmpty &&
                              _selectedIndustry != null) {
                            // Create new project object
                            final newProject = Project(
                              projectName: _projectNameController.text,
                              createdAt: DateTime.now(),
                              lastUpdated: DateTime.now(),
                              status: 'Discovery 0% / Development 0%',
                            );

                            // Store project name for success message
                            final projectName = _projectNameController.text;

                            // Add new project to the beginning of the list
                            setState(() {
                              _allProjects.insert(0, newProject);
                              _filteredProjects.insert(0, newProject);
                              projects.insert(0, newProject);
                              _updatePagination();

                              // Close modal and reset form
                              _showCreateProjectModal = false;
                              _projectNameController.clear();
                              _selectedIndustry = null;
                            });

                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Project "$projectName" created successfully!'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xff0058FF),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        child: Text(
                          'Start',
                          style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyLarge(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              height: 1),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _HoverTableRow extends StatefulWidget {
  final Project project;
  final bool isEvenRow;
  final VoidCallback onEditPressed;

  const _HoverTableRow({
    required this.project,
    required this.isEvenRow,
    required this.onEditPressed,
  });

  @override
  State<_HoverTableRow> createState() => _HoverTableRowState();
}

class _HoverTableRowState extends State<_HoverTableRow> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: widget.isEvenRow ? Colors.white : Color(0xFFF7F9FB),
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.shade200,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Text(
                widget.project.projectName ?? '',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Text(
                DateFormat('MMMM d, yyyy h:mm a').format(
                    widget.project.createdAt?.toLocal() ?? DateTime.now()),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Text(
                DateFormat('MMMM d, yyyy h:mm a').format(
                    widget.project.lastUpdated?.toLocal() ?? DateTime.now()),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Text(
                widget.project.tenant ?? '',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Text(
                widget.project.status ?? '',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: _buildEditButton(context),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildEditButton(BuildContext context) {
    final editButton = InkWell(
      onTap: widget.onEditPressed,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        width: 70,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xff000000)),
          borderRadius: BorderRadius.circular(6),
          color: Colors.transparent,
        ),
        child: Center(
          child: Text(
            'Edit',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Color(0xff000000),
            ),
          ),
        ),
      ),
    );

    // Show with hover animation on desktop, always visible on mobile/tablet
    return ResponsiveFontSizes.isDesktop(context)
        ? AnimatedOpacity(
            opacity: isHovered ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: editButton,
          )
        : editButton;
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          border: Border.all(
            color: isDisabled
                ? Colors.grey.shade200
                : isHovered
                    ? Color(0xff0058FF)
                    : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered && !isDisabled ? BorderRadius.zero : null,
          color: isDisabled ? Colors.grey.shade50 : Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isDisabled
              ? Colors.grey.shade400
              : isHovered
                  ? Color(0xff0058FF)
                  : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
