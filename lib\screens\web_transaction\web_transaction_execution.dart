import 'dart:async';
import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import '../../models/workflow/workflow_instance_model.dart';
import '../../models/workflow/workflow_start_model.dart';
import '../../models/workflow/local_objective_inputs_model.dart';
import '../../models/workflow.dart';
import '../../services/workflow_instance_service.dart';
import '../../services/auth_service.dart';
import '../../services/service_locator.dart';
import '../../utils/logger.dart';
import '../../utils/widget_factory.dart' as utils_widget_factory;
import '../../widgets/common/nsl_knowledge_loader.dart';
import '../../widgets/input_field_widget.dart';

/// Enum to identify input types
enum InputType { user, system, info, dependent }

/// Helper class to combine input with its type
class InputWithType {
  final Input input;
  final InputType type;

  InputWithType(this.input, this.type);
}

class WebTransactionExecution extends StatefulWidget {
  final String objectiveId;
  final String? objectiveName;
  final bool isLeftSideExpanded;
  final bool isMobile;

  const WebTransactionExecution({
    super.key,
    required this.objectiveId,
    this.objectiveName,
    this.isLeftSideExpanded = false,
    this.isMobile = false,
  });

  @override
  State<WebTransactionExecution> createState() =>
      _WebTransactionExecutionState();
}

class _WebTransactionExecutionState extends State<WebTransactionExecution> {
  // Services
  final WorkflowInstanceService _workflowInstanceService =
      WorkflowInstanceService();
  final AuthService _authService = ServiceLocator().authService;

  // State variables
  bool _isLoading = false;
  bool _isSubmitting = false;
  String? _errorMessage;
  WorkflowInstanceModel? _workflowInstance;
  WorkflowStartModel? _workflowStart;
  LocalObjectiveInputsModel? _localObjectiveInputs;

  // Form state variables
  final Map<String, GlobalKey<InputFieldWidgetState>> _inputFieldKeys = {};
  final Map<String, dynamic> _formValues = {};
  List<InputField> _userInputFields = [];

  // Dependent field state variables
  final Map<String, List<Map<String, dynamic>>> _dependentOptions = {};
  final Map<String, bool> _dependentFieldsLoading = {};
  final Map<String, List<String>> _fieldDependencies =
      {}; // Maps parent field ID to list of dependent field IDs
  final Map<String, Timer?> _debounceTimers =
      {}; // Debounce timers for API calls
  String? _tenantId;
  String? _instanceId;

  String? _entityName;

  @override
  void initState() {
    super.initState();
    _initializeWorkflow();
  }

  @override
  void dispose() {
    // Cancel all debounce timers
    for (final timer in _debounceTimers.values) {
      timer?.cancel();
    }
    _debounceTimers.clear();
    super.dispose();
  }

  /// Initialize workflow by making sequential API calls
  Future<void> _initializeWorkflow() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get tenant_id and user_id from AuthService
      final savedAuthData = await _authService.getSavedAuthData();
      String tenantId =
          savedAuthData.data?.user?.tenantId ?? 't001'; // Default fallback
      String? userId = savedAuthData.data?.user?.id ?? '';

      Logger.info('Initializing workflow for objective: ${widget.objectiveId}');
      Logger.info('Using tenant_id: $tenantId, user_id: $userId');

      // API Call 1: Create Workflow Instance
      final workflowInstance =
          await _workflowInstanceService.createWorkflowInstance(
        objectiveId: widget.objectiveId,
        tenantId: tenantId,
        userId: userId,
      );

      setState(() {
        _workflowInstance = workflowInstance;
      });

      Logger.info(
          'Workflow instance created with ID: ${workflowInstance.instanceId}');

      // API Call 2: Start Workflow (using instance_id from first call)
      if (workflowInstance.instanceId != null) {
        final workflowStart =
            await _workflowInstanceService.startWorkflowInstance(
          instanceId: workflowInstance.instanceId!,
          tenantId: tenantId,
          userId: userId,
        );

        setState(() {
          _workflowStart = workflowStart;
        });

        Logger.info(
            'Workflow started successfully with status: ${workflowStart.status}');

        // API Call 3: Fetch Local Objective Inputs
        final localObjectiveInputs =
            await _workflowInstanceService.fetchLocalObjectiveInputs(
          instanceId: workflowInstance.instanceId!,
          tenantId: tenantId,
        );

        setState(() {
          _localObjectiveInputs = localObjectiveInputs;
          // Convert Input objects to InputField objects for form building
          // Include both user inputs and dependent inputs
          final allInputs = <Input>[
            ...(localObjectiveInputs.userInputs ?? []),
            ...(localObjectiveInputs.dependentInputs ?? []),
          ];
          _userInputFields = _convertInputsToInputFields(allInputs);
           if(allInputs.isNotEmpty) {
             _entityName = (allInputs ?? [])[0].entityName;
           }
          // Store tenant ID and instance ID for dependent field API calls
          _tenantId = tenantId;
          _instanceId = workflowInstance.instanceId;
          // Build dependency map
          _buildDependencyMap();
          _isLoading = false;
        });

        Logger.info('Local objective inputs fetched successfully');
        Logger.info(
            'User inputs count: ${localObjectiveInputs.userInputs?.length ?? 0}');
        Logger.info(
            'System inputs count: ${localObjectiveInputs.systemInputs?.length ?? 0}');
        Logger.info(
            'Info inputs count: ${localObjectiveInputs.infoInputs?.length ?? 0}');
        Logger.info(
            'Converted ${_userInputFields.length} user input fields for form building');
        Logger.info(
            'Built dependency map with ${_fieldDependencies.length} parent fields');
      } else {
        throw Exception(
            'Failed to get instance ID from workflow creation response');
      }
    } catch (e) {
      Logger.error('Error initializing workflow: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });

      // Show alert with error message
      if (mounted) {
        _showErrorAlert(e.toString());
      }
    }
  }

  /// Convert Input objects from LocalObjectiveInputsModel to InputField objects
  List<InputField> _convertInputsToInputFields(List<Input> inputs) {
    return inputs.map((input) {
      return InputField(
        inputId: input.itemId ?? '',
        inputStackId: input.inputStackId != null
            ? int.tryParse(input.inputStackId!)
            : null,
        attributeId: input.attributeId ?? '',
        entityId: input.entityId ?? '',
        displayName:
            input.displayName ?? input.attributeName ?? 'Unknown Field',
        dataType: input.dataType ?? 'String',
        sourceType: input.sourceType ?? 'user',
        required: input.required ?? false,
        uiControl: input.uiControl ?? 'oj-input-text',
        isVisible: input.isVisible ?? true,
        readOnly:
            input.readOnly ?? false, // Transfer readOnly property from Input
        allowedValues: input.enumValues,
        validations: null, // Convert if needed
        contextualId: input.contextualId ?? '',
        inputValue: input.inputValue!=null ? input.inputValue is List ? getStringFromList(input.inputValue.cast<String>(),input.defaultValue) : input.inputValue : input.defaultValue,
        hasDropdownSource: input.hasDropdownSource ?? false,
        dependencyType: input.dependencyType?.toString(),
        metadata: InputFieldMetadata(
          usage: '',
          isInformational: input.informationField ?? false,
          hasDropdownSource: input.hasDropdownSource ?? false,
        ),
        dropdownOptions: null, // Convert if needed
        needsParentValue: input.needsParentValue,
        parentIds: input.parentIds?.map((id) => id.toString()).toList(),
      );
    }).toList();
  }

  /// Build dependency map using dependent_attribute_ids
  void _buildDependencyMap() {
    _fieldDependencies.clear();

    if (_localObjectiveInputs == null) return;

    // Check dependent inputs for dependency relationships
    final dependentInputs = _localObjectiveInputs!.dependentInputs ?? [];

    for (final dependentInput in dependentInputs) {
      // Check if this is a dependent input with dependent_attribute_ids
      if (dependentInput.dependentAttribute == true &&
          dependentInput.dependentAttributeIds != null &&
          dependentInput.dependentAttributeIds!.isNotEmpty) {
        // Iterate through each dependent_attribute_id mapping
        dependentInput.dependentAttributeIds!
            .forEach((attributeName, parentItemId) {
          // Find the parent field by parentItemId in all inputs (user, system, info, dependent)
          final allInputs = <Input>[
            ...(_localObjectiveInputs!.userInputs ?? []),
            ...(_localObjectiveInputs!.systemInputs ?? []),
            ...(_localObjectiveInputs!.infoInputs ?? []),
            ...(_localObjectiveInputs!.dependentInputs ?? []),
          ];

          final parentInput = allInputs.firstWhere(
            (input) =>
                input.itemId == parentItemId ||
                input.contextualId == parentItemId,
            orElse: () => Input(), // Return empty Input if not found
          );

          // Use parent field's slotId as the key (fallback to attributeId if slotId is null)
          final parentFieldKey =
              parentInput.slotId ?? parentInput.attributeId ?? parentItemId;

          final dependentFieldId =
              dependentInput.attributeId ?? dependentInput.itemId ?? '';

          if (dependentFieldId.isNotEmpty && parentFieldKey.isNotEmpty) {
            if (!_fieldDependencies.containsKey(parentFieldKey)) {
              _fieldDependencies[parentFieldKey] = [];
            }
            _fieldDependencies[parentFieldKey]!.add(dependentFieldId);
            Logger.info(
                'Added dependency: $dependentFieldId depends on $parentFieldKey (slot_id) via dependent_attribute_ids[$attributeName] = $parentItemId');
          }
        });
      }
    }

    Logger.info(
        'Built dependency map with ${_fieldDependencies.length} parent fields');
  }

  /// Handle form field value changes with debounce for dependent field API calls
  void _handleInputValueChanged(
      String sectionId, String attributeId, dynamic value, String inputId) {
    // Find the input to get its displayName for the new key format
    final allInputs = <Input>[
      ...(_localObjectiveInputs?.userInputs ?? []),
      ...(_localObjectiveInputs?.systemInputs ?? []),
      ...(_localObjectiveInputs?.infoInputs ?? []),
      ...(_localObjectiveInputs?.dependentInputs ?? []),
    ];

    final currentInput = allInputs.firstWhere(
      (input) => input.attributeId == attributeId || input.itemId == inputId,
      orElse: () => Input(), // Return empty Input if not found
    );

    // Use displayName as the key for form values storage
    final displayName =
         currentInput.attributeName ?? attributeId;

    setState(() {
      _formValues[displayName] = value;
    });
    Logger.info('Form value changed: $displayName = $value');

    // Use slotId as the key for dependency checking (fallback to attributeId)
    final fieldKey = currentInput.slotId ?? attributeId;

    // Check if this field has dependent fields using slotId
    if (_fieldDependencies.containsKey(fieldKey)) {
      final dependentFieldIds = _fieldDependencies[fieldKey]!;
      Logger.info(
          'Field $fieldKey (slot_id) has ${dependentFieldIds.length} dependent fields');

      // Cancel existing timer for this field if any
      _debounceTimers[fieldKey]?.cancel();

      // Set up new debounce timer (500ms delay)
      _debounceTimers[fieldKey] = Timer(Duration(milliseconds: 500), () {
        Logger.info('Debounce timer triggered for field $fieldKey (slot_id)');

        // Update dependent fields after debounce
        for (final dependentFieldId in dependentFieldIds) {
          _updateDependentField(dependentFieldId, fieldKey, value);
        }

        // Clear the timer reference
        _debounceTimers[fieldKey] = null;
      });
    }
  }

  /// Update dependent field options based on parent field value
  /// Now checks if all required parent values are available before making API call
  Future<void> _updateDependentField(String dependentFieldId,
      String parentFieldId, dynamic parentValue) async {
    Logger.info('=== DEPENDENT FIELD UPDATE START ===');
    Logger.info('Dependent Field ID: $dependentFieldId');
    Logger.info('Parent Field ID: $parentFieldId');
    Logger.info('Parent Value: $parentValue');

    if (_instanceId == null || _tenantId == null) {
      Logger.error(
          'Instance ID or Tenant ID not available for dependent field update');
      return;
    }

    // Find the dependent field in dependent inputs (not user inputs)
    final dependentInputs = _localObjectiveInputs?.dependentInputs ?? [];
    final dependentInput = dependentInputs.firstWhere(
      (input) => (input.attributeId ?? input.itemId ?? '') == dependentFieldId,
      orElse: () {
        Logger.error(
            'Dependent input $dependentFieldId not found in dependent inputs');
        return Input(); // Return empty Input if not found
      },
    );

    // Check if we found a valid dependent input
    if (dependentInput.itemId == null && dependentInput.attributeId == null) {
      Logger.error('No valid dependent input found for $dependentFieldId');
      return;
    }

    // Check if this dependent field has multiple parent dependencies
    if (dependentInput.dependentAttributeIds != null &&
        dependentInput.dependentAttributeIds!.length > 1) {
      Logger.info(
          'Dependent field $dependentFieldId has ${dependentInput.dependentAttributeIds!.length} parent dependencies');

      // Check if all parent values are available
      final allParentValues = <String, String>{};
      bool allParentsHaveValues = true;

      for (final entry in dependentInput.dependentAttributeIds!.entries) {
        final attributeName = entry.key;
        final parentItemId = entry.value;

        // Find the parent input
        final allInputs = <Input>[
          ...(_localObjectiveInputs!.userInputs ?? []),
          ...(_localObjectiveInputs!.systemInputs ?? []),
          ...(_localObjectiveInputs!.infoInputs ?? []),
          ...(_localObjectiveInputs!.dependentInputs ?? []),
        ];

        final parentInput = allInputs.firstWhere(
          (input) =>
              input.itemId == parentItemId ||
              input.contextualId == parentItemId,
          orElse: () => Input(),
        );

        if (parentInput.attributeId != null) {
          final parentDisplayName = parentInput.displayName ??
              parentInput.attributeName ??
              parentInput.attributeId ??
              '';
          final parentValue = _formValues[parentDisplayName];
          final parentValueStr = parentValue?.toString() ?? '';

          if (parentValueStr.isNotEmpty) {
            allParentValues[attributeName] = parentValueStr;
            Logger.info(
                'Parent $attributeName ($parentItemId) has value: $parentValueStr');
          } else {
            allParentsHaveValues = false;
            Logger.info(
                'Parent $attributeName ($parentItemId) has no value yet');
          }
        } else {
          allParentsHaveValues = false;
          Logger.info('Parent input $parentItemId not found');
        }
      }

      if (!allParentsHaveValues) {
        Logger.info(
            'Not all parent values are available yet. Waiting for all dependencies.');
        setState(() {
          _dependentOptions[dependentFieldId] = [];
          _dependentFieldsLoading[dependentFieldId] = false;
          _forceWidgetRebuild(dependentFieldId);
        });
        return;
      }

      Logger.info('All parent values are available. Proceeding with API call.');
      Logger.info('Parent values: $allParentValues');

      // Use contextualId or itemId for the API call
      final inputId = dependentInput.contextualId?.isNotEmpty == true
          ? dependentInput.contextualId!
          : dependentInput.itemId ?? '';

      if (inputId.isEmpty) {
        Logger.error(
            'No valid input ID found for dependent field $dependentFieldId');
        return;
      }

      try {
        Logger.info(
            'Setting loading state for dependent field $dependentFieldId');
        setState(() {
          _dependentFieldsLoading[dependentFieldId] = true;
          _forceWidgetRebuild(dependentFieldId);
        });

        // Create a combined parent value string (you may need to adjust this based on API requirements)
        final combinedParentValue = allParentValues.values.join(',');
        Logger.info(
            'Fetching dependent options for field $dependentFieldId with combined parent values: $combinedParentValue');

        final options = await _workflowInstanceService.fetchDependentOptions(
          instanceId: _instanceId!,
          inputId: inputId,
          parentValue: combinedParentValue,
          tenantId: _tenantId!,
        );

        Logger.info('API call completed. Received ${options.length} options:');
        for (int i = 0; i < options.length; i++) {
          Logger.info('  Option $i: ${options[i]}');
        }

        setState(() {
          _dependentOptions[dependentFieldId] = options;
          _dependentFieldsLoading[dependentFieldId] = false;
          _forceWidgetRebuild(dependentFieldId);
        });

        Logger.info(
            'State updated. Current dependent options for $dependentFieldId: ${_dependentOptions[dependentFieldId]?.length ?? 0} options');
      } catch (e) {
        Logger.error('Error updating dependent field $dependentFieldId: $e');
        setState(() {
          _dependentOptions[dependentFieldId] = [];
          _dependentFieldsLoading[dependentFieldId] = false;
          _forceWidgetRebuild(dependentFieldId);
        });

        if (mounted) {
          final displayName = dependentInput.displayName ??
              dependentInput.attributeName ??
              'Unknown Field';
          _showErrorAlert('Failed to load options for $displayName: $e');
        }
      }
    } else {
      // Single parent dependency - use original logic
      Logger.info(
          'Dependent field $dependentFieldId has single parent dependency');

      final inputId = dependentInput.contextualId?.isNotEmpty == true
          ? dependentInput.contextualId!
          : dependentInput.itemId ?? '';

      if (inputId.isEmpty) {
        Logger.error(
            'No valid input ID found for dependent field $dependentFieldId');
        return;
      }

      final parentValueStr = parentValue?.toString() ?? '';

      if (parentValueStr.isEmpty) {
        Logger.info('Parent value is empty, clearing dependent field options');
        setState(() {
          _dependentOptions[dependentFieldId] = [];
          _dependentFieldsLoading[dependentFieldId] = false;
          _forceWidgetRebuild(dependentFieldId);
        });
        return;
      }

      try {
        Logger.info(
            'Setting loading state for dependent field $dependentFieldId');
        setState(() {
          _dependentFieldsLoading[dependentFieldId] = true;
          _forceWidgetRebuild(dependentFieldId);
        });

        Logger.info(
            'Fetching dependent options for field $dependentFieldId with parent value: $parentValueStr');

        final options = await _workflowInstanceService.fetchDependentOptions(
          instanceId: _instanceId!,
          inputId: inputId,
          parentValue: parentValueStr,
          tenantId: _tenantId!,
        );

        Logger.info('API call completed. Received ${options.length} options:');
        for (int i = 0; i < options.length; i++) {
          Logger.info('  Option $i: ${options[i]}');
        }

        setState(() {
          _dependentOptions[dependentFieldId] = options;
          _dependentFieldsLoading[dependentFieldId] = false;
          _forceWidgetRebuild(dependentFieldId);
        });

        Logger.info(
            'State updated. Current dependent options for $dependentFieldId: ${_dependentOptions[dependentFieldId]?.length ?? 0} options');
      } catch (e) {
        Logger.error('Error updating dependent field $dependentFieldId: $e');
        setState(() {
          _dependentOptions[dependentFieldId] = [];
          _dependentFieldsLoading[dependentFieldId] = false;
          _forceWidgetRebuild(dependentFieldId);
        });

        if (mounted) {
          final displayName = dependentInput.displayName ??
              dependentInput.attributeName ??
              'Unknown Field';
          _showErrorAlert('Failed to load options for $displayName: $e');
        }
      }
    }

    Logger.info('=== DEPENDENT FIELD UPDATE COMPLETE ===');
  }

  /// Force widget rebuild by creating a new key for the dependent field
  void _forceWidgetRebuild(String dependentFieldId) {
    // Find the dependent field to get its properties for unique identifier
    final dependentInputs = _localObjectiveInputs?.dependentInputs ?? [];
    final dependentInput = dependentInputs.firstWhere(
      (input) => (input.attributeId ?? input.itemId ?? '') == dependentFieldId,
      orElse: () => Input(),
    );

    // Create the same unique field identifier as used in _buildFormField
    final attributeId = dependentInput.attributeId ?? '';
    final inputId = dependentInput.itemId ?? '';
    final displayName = dependentInput.displayName ??
        dependentInput.attributeName ??
        dependentFieldId;
    final fieldKey = '${attributeId}_${inputId}_${displayName}';

    Logger.info('Forcing widget rebuild for field: $fieldKey');

    // Create a new key with timestamp to force widget rebuild
    final newKey = GlobalKey<InputFieldWidgetState>();
    _inputFieldKeys[fieldKey] = newKey;

    Logger.info('Created new key for field $fieldKey to force rebuild');
  }

  /// Show error alert dialog
  void _showErrorAlert(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Error'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('OK'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _initializeWorkflow(); // Retry
              },
              child: Text('Retry'),
            ),
          ],
        );
      },
    );
  }

  /// Show success alert dialog
  void _showSuccessAlert(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Success'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Submit form data to execute local objective
  Future<void> _submitForm() async {
    if (_instanceId == null || _tenantId == null) {
      _showErrorAlert('Instance ID or Tenant ID not available');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Get user ID from AuthService
      final userId = await _authService.getUserId();
      if (userId == null || userId.isEmpty) {
        throw Exception('User ID not available');
      }

      Logger.info('Submitting form with ${_formValues.length} field values');
      Logger.info('Form values: $_formValues');

      // Collect all form input values for the input_data object
      final inputData = <String, dynamic>{};

      // CRITICAL FIX: Collect values directly from form field widgets
      // This ensures we capture ALL field values, including pre-filled ones
      final allInputs = <Input>[
        ...(_localObjectiveInputs?.userInputs ?? []),
        ...(_localObjectiveInputs?.systemInputs ?? []),
        ...(_localObjectiveInputs?.infoInputs ?? []),
        ...(_localObjectiveInputs?.dependentInputs ?? []),
      ];

      Logger.info('Collecting values from ${_inputFieldKeys.length} form fields');

      // First, collect values directly from form field widgets
      for (final entry in _inputFieldKeys.entries) {
        final fieldKey = entry.key;
        final fieldWidget = entry.value;

        try {
          // Get the current value from the widget
          final currentValue = fieldWidget.currentState?.getValue();

          if (currentValue != null) {
            // Parse the fieldKey to extract attributeId, inputId, and displayName
            // fieldKey format: '${field.attributeId}_${field.inputId}_${field.displayName}'
            final keyParts = fieldKey.split('_');

            // Find the corresponding input using the parsed attributeId
            final attributeId = keyParts.isNotEmpty ? keyParts[0] : '';
            final input = allInputs.firstWhere(
              (input) => input.attributeId == attributeId,
              orElse: () => Input(),
            );

            final displayName = input.displayName ??
                               input.attributeName ??
                               input.attributeId ??
                               'Unknown Field';

            inputData[displayName] = currentValue;
            Logger.info('Collected from widget: $displayName = $currentValue (fieldKey: $fieldKey)');
          } else {
            Logger.info('No value found for field: $fieldKey');
          }
        } catch (e) {
          Logger.error('Error collecting value from field $fieldKey: $e');
        }
      }

      // Then, overlay any values from _formValues (these are more recent changes)
      for (final entry in _formValues.entries) {
        final displayName = entry.key;
        final value = entry.value;
        inputData[displayName] = value;
        Logger.info('Overlaid from _formValues: $displayName = $value');
      }

      Logger.info('Final collected input data: $inputData');

      // Call the execution API
      final result = await _workflowInstanceService.executeLocalObjective(
        instanceId: _instanceId!,
        tenantId: _tenantId!,
        inputData: inputData,
        userId: userId,
      );

      Logger.info('Local objective execution completed successfully');
      Logger.info('Result: ${result.toJson()}');

      // Check if there's a next_lo_id in the response
      if (result.nextLoId != null && result.nextLoId!.isNotEmpty) {
        Logger.info(
            'Next LO ID found: ${result.nextLoId}. Proceeding to next Local Objective.');

        // Clear current form state
        _clearFormState();

        // Fetch inputs for the next Local Objective
        await _fetchNextLocalObjective();

        // Show success message for current LO completion
        final successMessage = result.message ??
            'Local objective completed successfully. Loading next objective...';
        // _showSuccessAlert(successMessage);
      } else {
        Logger.info('No next LO ID found. Workflow execution completed.');

        // Show final success message
        final successMessage =
            result.message ?? 'Workflow execution completed successfully';
        _showSuccessAlert(successMessage);
      }
    } catch (e) {
      Logger.error('Error submitting form: $e');
      _showErrorAlert(e.toString());
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  /// Clear current form state to prepare for next LO
  void _clearFormState() {
    setState(() {
      _formValues.clear();
      _inputFieldKeys.clear();
      _userInputFields.clear();
      _dependentOptions.clear();
      _dependentFieldsLoading.clear();
      _fieldDependencies.clear();
    });

    // Cancel all debounce timers
    for (final timer in _debounceTimers.values) {
      timer?.cancel();
    }
    _debounceTimers.clear();

    Logger.info('Form state cleared for next Local Objective');
  }

  /// Fetch inputs for the next Local Objective
  Future<void> _fetchNextLocalObjective() async {
    if (_instanceId == null || _tenantId == null) {
      throw Exception('Instance ID or Tenant ID not available for next LO');
    }

    try {
      Logger.info('Fetching inputs for next Local Objective');

      setState(() {
        _isLoading = true;
      });

      // API Call: Fetch Local Objective Inputs for the next LO
      final localObjectiveInputs =
          await _workflowInstanceService.fetchLocalObjectiveInputs(
        instanceId: _instanceId!,
        tenantId: _tenantId!,
      );

      setState(() {
        _localObjectiveInputs = localObjectiveInputs;
        // Convert Input objects to InputField objects for form building
        // Include both user inputs and dependent inputs
        final allInputs = <Input>[
          ...(localObjectiveInputs.userInputs ?? []),
          ...(localObjectiveInputs.dependentInputs ?? []),
        ];
        _userInputFields = _convertInputsToInputFields(allInputs);
        // Build dependency map for the new LO
        _buildDependencyMap();
        _isLoading = false;
      });

      Logger.info('Next Local Objective inputs fetched successfully');
      Logger.info(
          'User inputs count: ${localObjectiveInputs.userInputs?.length ?? 0}');
      Logger.info(
          'System inputs count: ${localObjectiveInputs.systemInputs?.length ?? 0}');
      Logger.info(
          'Info inputs count: ${localObjectiveInputs.infoInputs?.length ?? 0}');
      Logger.info(
          'Dependent inputs count: ${localObjectiveInputs.dependentInputs?.length ?? 0}');
      Logger.info(
          'Converted ${_userInputFields.length} input fields for next LO form building');
      Logger.info(
          'Built dependency map with ${_fieldDependencies.length} parent fields for next LO');
    } catch (e) {
      Logger.error('Error fetching next Local Objective inputs: $e');
      setState(() {
        _isLoading = false;
      });
      throw Exception('Failed to load next Local Objective: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return NSLKnowledgeLoaderWrapper(
        isLoading: _isLoading || _isSubmitting,
        child: Scaffold(
          backgroundColor: Color(0xffF7F9FB),
          body: widget.isMobile
              ? Padding(
                  padding: EdgeInsets.only(top: AppSpacing.sm),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      // Text(
                      //   'Transaction Execution',
                      //   style: TextStyle(
                      //     fontSize: 24,
                      //     fontWeight: FontWeight.bold,
                      //     color: Colors.black87,
                      //   ),
                      // ),
                      // SizedBox(height: 8),
                      if (widget.objectiveName != null)
                        RichText(
                            text: TextSpan(children: [
                          TextSpan(
                            text: 'Objective: ',
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          TextSpan(
                            text: _entityName??widget.objectiveName,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                          ),
                        ])),
                      SizedBox(height: AppSpacing.xs),

                      // Content
                      Expanded(
                        child: _buildContent(),
                      ),
                    ],
                  ),
                )
              : SafeArea(
                  child: Padding(
                    padding: EdgeInsets.all(AppSpacing.md),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        // Text(
                        //   'Transaction Execution',
                        //   style: TextStyle(
                        //     fontSize: 24,
                        //     fontWeight: FontWeight.bold,
                        //     color: Colors.black87,
                        //   ),
                        // ),
                        // SizedBox(height: 8),
                        if (widget.objectiveName != null)
                          RichText(
                              text: TextSpan(children: [
                            TextSpan(
                              text: 'Objective: ',
                              style: TextStyle(
                                fontSize: 16,
                                fontFamily: 'TiemposText',
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            TextSpan(
                              text: _entityName??widget.objectiveName,
                              style: TextStyle(
                                fontSize: 16,
                                fontFamily: 'TiemposText',
                                color: Colors.black,
                              ),
                            ),
                          ])),
                        SizedBox(height: 24),

                        // Content
                        Expanded(
                          child: _buildContent(),
                        ),
                      ],
                    ),
                  ),
                ),
        ));
  }

  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingWidget();
    } else if (_errorMessage != null) {
      return _buildErrorWidget();
    } else if (_workflowStart != null && _localObjectiveInputs != null) {
      return _buildSuccessWidget();
    } else {
      return Center(
        child: Text('Initializing...'),
      );
    }
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Text(
        'Initializing workflow and fetching inputs...',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          SizedBox(height: 16),
          Text(
            'Error',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          SizedBox(height: 8),
          Text(
            _errorMessage ?? 'An unknown error occurred',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          ElevatedButton(
            onPressed: _initializeWorkflow,
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xff0058FF),
              foregroundColor: Colors.white,
            ),
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessWidget() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // // Success message
          // Container(
          //   padding: EdgeInsets.all(16),
          //   decoration: BoxDecoration(
          //     color: Colors.green[50],
          //     border: Border.all(color: Colors.green[200]!),
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          //   child: Row(
          //     children: [
          //       Icon(Icons.check_circle, color: Colors.green),
          //       SizedBox(width: 8),
          //       Expanded(
          //         child: Text(
          //           'Workflow initialized and all inputs loaded successfully!',
          //           style: TextStyle(
          //             color: Colors.green[700],
          //             fontWeight: FontWeight.w500,
          //           ),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // SizedBox(height: 24),

          // Comprehensive form with all input types
          _buildComprehensiveForm(),
        ],
      ),
    );
  }

  /// Build comprehensive form with all input types sorted by ID
  Widget _buildComprehensiveForm() {
    if (_localObjectiveInputs == null) {
      return SizedBox.shrink();
    }

    // Collect all inputs from different categories with their types
    final List<InputWithType> allInputsWithTypes = [];

    // Add user inputs
    final userInputs = _localObjectiveInputs!.userInputs ?? [];
    for (final input in userInputs) {
      allInputsWithTypes.add(InputWithType(input, InputType.user));
    }

    // Add system inputs
    final systemInputs = _localObjectiveInputs!.systemInputs ?? [];
    for (final input in systemInputs) {
      allInputsWithTypes.add(InputWithType(input, InputType.system));
    }

    // Add info inputs
    final infoInputs = _localObjectiveInputs!.infoInputs ?? [];
    for (final input in infoInputs) {
      allInputsWithTypes.add(InputWithType(input, InputType.info));
    }

    // Add dependent inputs
    final dependentInputs = _localObjectiveInputs!.dependentInputs ?? [];
    for (final input in dependentInputs) {
      allInputsWithTypes.add(InputWithType(input, InputType.dependent));
    }

    // Check if we have any inputs to display
    if (allInputsWithTypes.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          border: Border.all(color: Colors.blue[200]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.blue),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'No inputs required for this objective. The workflow is ready to proceed.',
                style: TextStyle(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Sort all inputs by ID in ascending order
    allInputsWithTypes.sort((a, b) {
      final aId = a.input.id ?? 0;
      final bId = b.input.id ?? 0;
      return aId > bId ? 1 : -1;
    });

    // Determine widgets per row based on left side expansion state
    int widgetsPerRow = widget.isLeftSideExpanded ? 3 : 4;

    // Adjust for smaller screens
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 1200) {
      widgetsPerRow = 2;
    } else if (screenWidth < 800) {
      widgetsPerRow = 1;
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xffD0D0D0)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // // Single unified section header
          // Container(
          //   padding: EdgeInsets.all(12),
          //   decoration: BoxDecoration(
          //     color: Colors.blue.withValues(alpha: 0.1),
          //     border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          //   child: Row(
          //     children: [
          //       Icon(Icons.assignment, color: Colors.blue),
          //       SizedBox(width: 8),
          //       Expanded(
          //         child: Column(
          //           crossAxisAlignment: CrossAxisAlignment.start,
          //           children: [
          //             Text(
          //               'All Workflow Inputs (${allInputsWithTypes.length})',
          //               style: TextStyle(
          //                 fontSize: 16,
          //                 fontWeight: FontWeight.bold,
          //                 color: Colors.blue.withValues(alpha: 0.8),
          //               ),
          //             ),
          //             SizedBox(height: 4),
          //             Text(
          //               'Inputs sorted by ID - interactive fields require your input, read-only fields show system values',
          //               style: TextStyle(
          //                 fontSize: 12,
          //                 color: Colors.blue.withValues(alpha: 0.7),
          //               ),
          //             ),
          //           ],
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // SizedBox(height: 16),

          // Build unified grid with all inputs
          ..._buildUnifiedInputGrid(
              allInputsWithTypes, widget.isMobile ? 1 : widgetsPerRow),

          // Add spacing before submit button
          SizedBox(height: 32),

          // Submit button
          _buildSubmitButton(),
        ],
      ),
    );
  }

  /// Build submit button
  Widget _buildSubmitButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        widget.isMobile
            ? Expanded(
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitForm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xff0058FF),
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isSubmitting
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Submitting...',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'SFProText',
                              ),
                            ),
                          ],
                        )
                      : Text(
                          'Submit',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'SFProText',
                          ),
                        ),
                ),
              )
            : ElevatedButton(
                onPressed: _isSubmitting ? null : _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xff0058FF),
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isSubmitting
                    ? Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Submitting...',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'SFProText',
                            ),
                          ),
                        ],
                      )
                    : Text(
                        'Submit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'SFProText',
                        ),
                      ),
              ),
      ],
    );
  }

  /// Build unified input grid with all input types
  List<Widget> _buildUnifiedInputGrid(
      List<InputWithType> allInputsWithTypes, int widgetsPerRow) {
    List<Widget> widgets = [];

    // Group inputs into rows
    for (int i = 0; i < allInputsWithTypes.length; i += widgetsPerRow) {
      int endIndex = (i + widgetsPerRow < allInputsWithTypes.length)
          ? i + widgetsPerRow
          : allInputsWithTypes.length;
      final rowInputsWithTypes = allInputsWithTypes.sublist(i, endIndex);

      widgets.add(_buildUnifiedRow(rowInputsWithTypes));
    }

    return widgets;
  }

  /// Build a unified row with mixed input types
  Widget _buildUnifiedRow(List<InputWithType> rowInputsWithTypes) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...rowInputsWithTypes.asMap().entries.map((entry) {
            final index = entry.key;
            final inputWithType = entry.value;

            // Create widget based on input type
            final inputWidget = _buildUnifiedInputWidget(inputWithType);

            // Wrap in Expanded for equal width distribution
            final expandedWidget = Expanded(child: inputWidget);

            // Add spacing between fields (except after the last field)
            if (index < rowInputsWithTypes.length - 1) {
              return [expandedWidget, SizedBox(width: 16)];
            } else {
              return [expandedWidget];
            }
          }).expand((widgets) => widgets),
        ],
      ),
    );
  }

  /// Build a unified input widget with visual distinction based on type
  Widget _buildUnifiedInputWidget(InputWithType inputWithType) {
    final input = inputWithType.input;
    final type = inputWithType.type;

    // if (type == InputType.user) {
    //   // Interactive user input - convert to InputField and use form building logic
    //   final inputField = _convertInputToInputField(input);
    //   return _buildFormFieldWithTypeIndicator(inputField, type);
    // } else {
    //   // Read-only input - use utils widget factory
    //   return _buildReadOnlyInputWithTypeIndicator(input, type);
    // }
    final inputField = _convertInputToInputField(input);
    return _buildFormFieldWithTypeIndicator(inputField, type);
  }

  /// Convert single Input to InputField
  InputField _convertInputToInputField(Input input) {
    // Implement conditional readonly logic:
    // If field has readonly=true but no existing value, make it editable
    final hasExistingValue =
        (input.inputValue != null && input.inputValue!.isNotEmpty) ||
            (input.defaultValue != null && input.defaultValue!.isNotEmpty);
    final shouldBeReadOnly = (input.readOnly ?? false) && hasExistingValue;

    return InputField(
      inputId: input.itemId ?? '',
      inputStackId:
          input.inputStackId != null ? int.tryParse(input.inputStackId!) : null,
      attributeId: input.attributeId ?? '',
      entityId: input.entityId ?? '',
      displayName: input.displayName ?? input.attributeName ?? 'Unknown Field',
      dataType: input.dataType ?? 'String',
      sourceType: input.sourceType ?? 'user',
      required: input.required ?? false,
      uiControl: input.uiControl ?? 'oj-input-text',
      isVisible: input.isVisible ?? true,
      readOnly: shouldBeReadOnly, // Apply conditional readonly logic
      allowedValues: input.enumValues,
      validations: null,
      contextualId: input.contextualId ?? '',
      inputValue: input.inputValue!=null ? input.inputValue is List ? getStringFromList(input.inputValue.cast<String>(),input.defaultValue) : input.inputValue : input.defaultValue,
      hasDropdownSource: input.hasDropdownSource ?? false,
      dependencies: null,
      dependencyType: input.dependencyType?.toString(),
      metadata: InputFieldMetadata(
        usage: '',
        isInformational: input.informationField ?? false,
        hasDropdownSource: input.hasDropdownSource ?? false,
      ),
      dropdownOptions: null,
      needsParentValue: input.needsParentValue,
      parentIds: input.parentIds?.map((id) => id.toString()).toList(),
    );
  }

  /// Build form field with type indicator
  Widget _buildFormFieldWithTypeIndicator(InputField field, InputType type) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // // Type indicator
        // _buildTypeIndicator(type, true),
        // SizedBox(height: 4),
        // Form field
        _buildFormField(field),
      ],
    );
  }

  /// Build read-only input with type indicator
  Widget _buildReadOnlyInputWithTypeIndicator(Input input, InputType type) {
    // Convert Input to InputField for compatibility
    final inputField = InputField(
      inputId: input.itemId ?? '',
      inputStackId:
          input.inputStackId != null ? int.tryParse(input.inputStackId!) : null,
      attributeId: input.attributeId ?? '',
      entityId: input.entityId ?? '',
      displayName: input.displayName ?? input.attributeName ?? 'Unknown Field',
      dataType: input.dataType ?? 'String',
      sourceType: input.sourceType ?? 'system',
      required: false,
      uiControl: input.uiControl ?? 'oj-label',
      isVisible: input.isVisible ?? true,
      readOnly: true, // Always readonly for system/info inputs
      allowedValues: input.enumValues,
      validations: null,
      contextualId: input.contextualId ?? '',
      inputValue: input.inputValue!=null ? input.inputValue is List ? getStringFromList(input.inputValue.cast<String>(),input.defaultValue) : input.inputValue : input.defaultValue,
      hasDropdownSource: false,
      dependencies: null,
      dependencyType: input.dependencyType?.toString(),
      metadata: InputFieldMetadata(
        usage: '',
        isInformational: true,
        hasDropdownSource: false,
      ),
      dropdownOptions: null,
      needsParentValue: false,
      parentIds: null,
    );
    // if (inputField.displayName.isNotEmpty) {
    //   inputField.displayName = inputField.displayName.replaceFirst(
    //       inputField.displayName[0], inputField.displayName[0].toUpperCase());
    // }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // // Type indicator
        // _buildTypeIndicator(type, false),
        // SizedBox(height: 4),
        // Read-only field
        utils_widget_factory.WidgetFactory.createSystemInfoWidget(
          context: context,
          field: inputField,
        ),
      ],
    );
  }

  /// Build type indicator badge
  Widget _buildTypeIndicator(InputType type, bool isInteractive) {
    Color color;
    String label;
    IconData icon;

    switch (type) {
      case InputType.user:
        color = Colors.orange;
        label = 'USER';
        icon = Icons.edit;
        break;
      case InputType.system:
        color = Colors.blue;
        label = 'SYSTEM';
        icon = Icons.settings;
        break;
      case InputType.info:
        color = Colors.green;
        label = 'INFO';
        icon = Icons.info_outline;
        break;
      case InputType.dependent:
        color = Colors.purple;
        label = 'DEPENDENT';
        icon = Icons.link;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (isInteractive) ...[
            SizedBox(width: 4),
            Icon(Icons.keyboard, size: 10, color: color),
          ],
        ],
      ),
    );
  }

  /// Build a single row of form fields
  Widget _buildFormRow(List<InputField> rowFields) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...rowFields.asMap().entries.map((entry) {
            final index = entry.key;
            final field = entry.value;

            // Create field widget
            final fieldWidget = _buildFormField(field);

            // Wrap in Expanded for equal width distribution
            final expandedWidget = Expanded(child: fieldWidget);

            // Add spacing between fields (except after the last field)
            if (index < rowFields.length - 1) {
              return [expandedWidget, SizedBox(width: 16)];
            } else {
              return [expandedWidget];
            }
          }).expand((widgets) => widgets),
        ],
      ),
    );
  }

  /// Build a single form field widget
  Widget _buildFormField(InputField field) {
    // Create a unique field identifier using multiple properties to avoid conflicts
    final fieldId =
        '${field.attributeId}_${field.inputId}_${field.displayName}';

    // Create or reuse key for the field
    GlobalKey<InputFieldWidgetState> key;
    if (_inputFieldKeys.containsKey(fieldId)) {
      key = _inputFieldKeys[fieldId]!;
    } else {
      key = GlobalKey<InputFieldWidgetState>();
      _inputFieldKeys[fieldId] = key;
    }

    // Check if this is a dependent field
    // A field is dependent if it's in the dependency map as a dependent field
    final isDependentField = _fieldDependencies.values
        .any((dependentFields) => dependentFields.contains(field.attributeId));

    // field.displayName = field.displayName
    //     .replaceFirst(field.displayName[0], field.displayName[0].toUpperCase());

    // Handle dependent field visibility and options
    if (isDependentField) {
      return _buildDependentField(field, key, fieldId);
    } else {
      // Regular field - use utils widget factory for interactive inputs
      // Note: readOnly parameter removed to let field.readOnly property take precedence
      return utils_widget_factory.WidgetFactory.createInputWidget(
        context: context,
        field: field,
        key: key,
        sectionId: 'form',
        onValueChanged: _handleInputValueChanged,
      );
    }
  }

  /// Build a dependent field with dynamic options and loading states
  Widget _buildDependentField(
      InputField field, GlobalKey<InputFieldWidgetState> key, String fieldId) {
    final isLoading = _dependentFieldsLoading[field.attributeId] ?? false;
    final hasParentValue = _hasParentValue(field);

    // Check if field should be visible based on parent values
    if (!hasParentValue) {
      return SizedBox.shrink(); // Hide field if parent has no value
    }

    // Create a modified field with dynamic options
    final modifiedField = _createFieldWithDependentOptions(field);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Show loading indicator if fetching options
        // if (isLoading) ...[
        //   Container(
        //     padding: EdgeInsets.all(12),
        //     decoration: BoxDecoration(
        //       color: Colors.blue[50],
        //       border: Border.all(color: Colors.blue[200]!),
        //       borderRadius: BorderRadius.circular(8),
        //     ),
        //     child: Row(
        //       children: [
        //         SizedBox(
        //           width: 16,
        //           height: 16,
        //           child: CircularProgressIndicator(
        //             strokeWidth: 2,
        //             valueColor:
        //                 AlwaysStoppedAnimation<Color>(Color(0xff0058FF)),
        //           ),
        //         ),
        //         SizedBox(width: 8),
        //         Text(
        //           'Loading options for ${field.displayName}...',
        //           style: TextStyle(
        //             fontSize: 12,
        //             color: Colors.blue[700],
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),
        //   SizedBox(height: 8),
        // ],

        // The actual form field - use utils widget factory for dependent inputs
        utils_widget_factory.WidgetFactory.createInputWidget(
          context: context,
          field: modifiedField,
          key: key,
          sectionId: 'form',
          readOnly: modifiedField.readOnly, // Make read-only while loading
          onValueChanged: _handleInputValueChanged,
        ),
      ],
    );
  }

  /// Check if dependent field has parent values
  bool _hasParentValue(InputField field) {
    // Find parent fields for this dependent field
    final parentFields = <String>[];

    for (final entry in _fieldDependencies.entries) {
      final parentFieldKey = entry.key;
      final dependentFields = entry.value;

      if (dependentFields.contains(field.attributeId)) {
        parentFields.add(parentFieldKey);
      }
    }

    if (parentFields.isEmpty) {
      return true; // Not a dependent field
    }

    // Check if at least one parent field has a value
    for (final parentFieldKey in parentFields) {
      // Try to find the parent field value using displayName
      final parentValue = _findParentValueBySlotId(parentFieldKey);
      if (parentValue != null && parentValue.toString().isNotEmpty) {
        return true;
      }
    }

    return false;
  }

  /// Find parent field value by slot_id
  dynamic _findParentValueBySlotId(String slotId) {
    // Find the user input with this slot_id
    final userInputs = _localObjectiveInputs?.userInputs ?? [];
    final parentInput = userInputs.firstWhere(
      (input) => input.slotId == slotId,
      orElse: () => Input(),
    );

    if (parentInput.attributeId != null) {
      // Use displayName as the key to find the value
      final displayName = parentInput.displayName ??
          parentInput.attributeName ??
          parentInput.attributeId ??
          '';
      return _formValues[displayName];
    }

    return null;
  }

  /// Create a field with dependent options applied
  InputField _createFieldWithDependentOptions(InputField originalField) {
    final options = _dependentOptions[originalField.attributeId] ?? [];

    Logger.info('=== CREATING FIELD WITH DEPENDENT OPTIONS ===');
    Logger.info(
        'Field: ${originalField.displayName} (${originalField.attributeId})');
    Logger.info('Available options: ${options.length}');
    for (int i = 0; i < options.length; i++) {
      Logger.info('  Option $i: ${options[i]}');
    }

    // Convert options to dropdown options format
    final dropdownOptions = options.map((option) {
      return DropdownOption(
        value: option['value'] ?? '',
        label: option['label'] ?? option['value'] ?? '',
      );
    }).toList();

    final allowedValues =
        options.map((option) => option['label'] as String).toList();

    Logger.info('Converted to allowedValues: $allowedValues');
    Logger.info(
        'Converted to dropdownOptions: ${dropdownOptions.map((o) => '${o.value}:${o.label}').toList()}');

    // Create a new field with updated dropdown options
    final newField = InputField(
      inputId: originalField.inputId,
      inputStackId: originalField.inputStackId,
      attributeId: originalField.attributeId,
      entityId: originalField.entityId,
      displayName: originalField.displayName,
      dataType: originalField.dataType,
      sourceType: originalField.sourceType,
      required: originalField.required,
      uiControl: originalField.uiControl,
      isVisible: originalField.isVisible,
      readOnly: originalField.readOnly, // Preserve readOnly property
      allowedValues: allowedValues,
      validations: originalField.validations,
      contextualId: originalField.contextualId,
      inputValue: originalField.inputValue,
      hasDropdownSource: true, // Enable dropdown source
      dependencyType: originalField.dependencyType,
      metadata: originalField.metadata,
      dropdownOptions: dropdownOptions,
      needsParentValue: originalField.needsParentValue,
      parentIds: originalField.parentIds,
    );

    Logger.info(
        'Created new field with ${newField.allowedValues?.length ?? 0} allowedValues and ${newField.dropdownOptions?.length ?? 0} dropdownOptions');
    Logger.info('=== FIELD CREATION COMPLETE ===');

    return newField;
  }

  getStringFromList(List<String> list, String? defaultValue) {
    if(list.isNotEmpty){
      return list.join(",");
    }else{
      return defaultValue;
    }


  }
}
