// To parse this JSON data, do
//
//     final parseValidationEntityModel = parseValidationEntityModelFromJson(jsonString);

import 'dart:convert';

ParseValidationEntityModel parseValidationEntityModelFromJson(String str) =>
    ParseValidationEntityModel.fromJson(json.decode(str));

String parseValidationEntityModelToJson(ParseValidationEntityModel data) =>
    json.encode(data.toJson());

class ParseValidationEntityModel {
  bool? success;
  ParsedData? parsedData;
  ValidationResult? validationResult;
  List<dynamic>? dependencyErrors;
  UniquenessResult? uniquenessResult;
  String? operation;
  bool? isValid;
  Issues? issues;
  bool? hasErrors;
  bool? hasWarnings;
  IssueCounts? issueCounts;

  ParseValidationEntityModel({
    this.success,
    this.parsedData,
    this.validationResult,
    this.dependencyErrors,
    this.uniquenessResult,
    this.operation,
    this.isValid,
    this.issues,
    this.hasErrors,
    this.hasWarnings,
    this.issueCounts,
  });

  ParseValidationEntityModel copyWith({
    bool? success,
    ParsedData? parsedData,
    ValidationResult? validationResult,
    List<dynamic>? dependencyErrors,
    UniquenessResult? uniquenessResult,
    String? operation,
    bool? isValid,
    Issues? issues,
    bool? hasErrors,
    bool? hasWarnings,
    IssueCounts? issueCounts,
  }) =>
      ParseValidationEntityModel(
        success: success ?? this.success,
        parsedData: parsedData ?? this.parsedData,
        validationResult: validationResult ?? this.validationResult,
        dependencyErrors: dependencyErrors ?? this.dependencyErrors,
        uniquenessResult: uniquenessResult ?? this.uniquenessResult,
        operation: operation ?? this.operation,
        isValid: isValid ?? this.isValid,
        issues: issues ?? this.issues,
        hasErrors: hasErrors ?? this.hasErrors,
        hasWarnings: hasWarnings ?? this.hasWarnings,
        issueCounts: issueCounts ?? this.issueCounts,
      );

  factory ParseValidationEntityModel.fromJson(Map<String, dynamic> json) =>
      ParseValidationEntityModel(
        success: json["success"],
        parsedData: json["parsed_data"] == null
            ? null
            : ParsedData.fromJson(json["parsed_data"]),
        validationResult: json["validation_result"] == null
            ? null
            : ValidationResult.fromJson(json["validation_result"]),
        dependencyErrors: json["dependency_errors"] == null
            ? []
            : List<dynamic>.from(json["dependency_errors"]!.map((x) => x)),
        uniquenessResult: json["uniqueness_result"] == null
            ? null
            : UniquenessResult.fromJson(json["uniqueness_result"]),
        operation: json["operation"],
        isValid: json["is_valid"],
        issues: json["issues"] == null ? null : Issues.fromJson(json["issues"]),
        hasErrors: json["has_errors"],
        hasWarnings: json["has_warnings"],
        issueCounts: json["issue_counts"] == null
            ? null
            : IssueCounts.fromJson(json["issue_counts"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "parsed_data": parsedData?.toJson(),
        "validation_result": validationResult?.toJson(),
        "dependency_errors": dependencyErrors == null
            ? []
            : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "uniqueness_result": uniquenessResult?.toJson(),
        "operation": operation,
        "is_valid": isValid,
        "issues": issues?.toJson(),
        "has_errors": hasErrors,
        "has_warnings": hasWarnings,
        "issue_counts": issueCounts?.toJson(),
      };
}

class IssueCounts {
  int? totalErrors;
  int? totalWarnings;
  int? totalExceptions;
  int? validationErrors;
  int? dependencyErrors;
  int? uniquenessIssues;
  int? parsingIssues;
  int? mongoErrors;
  int? postgresErrors;
  bool? hasCriticalErrors;
  bool? hasWarnings;

  IssueCounts({
    this.totalErrors,
    this.totalWarnings,
    this.totalExceptions,
    this.validationErrors,
    this.dependencyErrors,
    this.uniquenessIssues,
    this.parsingIssues,
    this.mongoErrors,
    this.postgresErrors,
    this.hasCriticalErrors,
    this.hasWarnings,
  });

  IssueCounts copyWith({
    int? totalErrors,
    int? totalWarnings,
    int? totalExceptions,
    int? validationErrors,
    int? dependencyErrors,
    int? uniquenessIssues,
    int? parsingIssues,
    int? mongoErrors,
    int? postgresErrors,
    bool? hasCriticalErrors,
    bool? hasWarnings,
  }) =>
      IssueCounts(
        totalErrors: totalErrors ?? this.totalErrors,
        totalWarnings: totalWarnings ?? this.totalWarnings,
        totalExceptions: totalExceptions ?? this.totalExceptions,
        validationErrors: validationErrors ?? this.validationErrors,
        dependencyErrors: dependencyErrors ?? this.dependencyErrors,
        uniquenessIssues: uniquenessIssues ?? this.uniquenessIssues,
        parsingIssues: parsingIssues ?? this.parsingIssues,
        mongoErrors: mongoErrors ?? this.mongoErrors,
        postgresErrors: postgresErrors ?? this.postgresErrors,
        hasCriticalErrors: hasCriticalErrors ?? this.hasCriticalErrors,
        hasWarnings: hasWarnings ?? this.hasWarnings,
      );

  factory IssueCounts.fromJson(Map<String, dynamic> json) => IssueCounts(
        totalErrors: json["total_errors"],
        totalWarnings: json["total_warnings"],
        totalExceptions: json["total_exceptions"],
        validationErrors: json["validation_errors"],
        dependencyErrors: json["dependency_errors"],
        uniquenessIssues: json["uniqueness_issues"],
        parsingIssues: json["parsing_issues"],
        mongoErrors: json["mongo_errors"],
        postgresErrors: json["postgres_errors"],
        hasCriticalErrors: json["has_critical_errors"],
        hasWarnings: json["has_warnings"],
      );

  Map<String, dynamic> toJson() => {
        "total_errors": totalErrors,
        "total_warnings": totalWarnings,
        "total_exceptions": totalExceptions,
        "validation_errors": validationErrors,
        "dependency_errors": dependencyErrors,
        "uniqueness_issues": uniquenessIssues,
        "parsing_issues": parsingIssues,
        "mongo_errors": mongoErrors,
        "postgres_errors": postgresErrors,
        "has_critical_errors": hasCriticalErrors,
        "has_warnings": hasWarnings,
      };
}

class Issues {
  IssueCounts? summary;
  List<dynamic>? errors;
  List<dynamic>? warnings;
  List<dynamic>? exceptions;
  List<dynamic>? validationErrors;
  List<dynamic>? dependencyErrors;
  List<UniquenessIssue>? uniquenessIssues;
  List<dynamic>? parsingIssues;
  List<dynamic>? mongoErrors;
  List<dynamic>? postgresErrors;

  Issues({
    this.summary,
    this.errors,
    this.warnings,
    this.exceptions,
    this.validationErrors,
    this.dependencyErrors,
    this.uniquenessIssues,
    this.parsingIssues,
    this.mongoErrors,
    this.postgresErrors,
  });

  Issues copyWith({
    IssueCounts? summary,
    List<dynamic>? errors,
    List<dynamic>? warnings,
    List<dynamic>? exceptions,
    List<dynamic>? validationErrors,
    List<dynamic>? dependencyErrors,
    List<UniquenessIssue>? uniquenessIssues,
    List<dynamic>? parsingIssues,
    List<dynamic>? mongoErrors,
    List<dynamic>? postgresErrors,
  }) =>
      Issues(
        summary: summary ?? this.summary,
        errors: errors ?? this.errors,
        warnings: warnings ?? this.warnings,
        exceptions: exceptions ?? this.exceptions,
        validationErrors: validationErrors ?? this.validationErrors,
        dependencyErrors: dependencyErrors ?? this.dependencyErrors,
        uniquenessIssues: uniquenessIssues ?? this.uniquenessIssues,
        parsingIssues: parsingIssues ?? this.parsingIssues,
        mongoErrors: mongoErrors ?? this.mongoErrors,
        postgresErrors: postgresErrors ?? this.postgresErrors,
      );

  factory Issues.fromJson(Map<String, dynamic> json) => Issues(
        summary: json["summary"] == null
            ? null
            : IssueCounts.fromJson(json["summary"]),
        errors: json["errors"] == null
            ? []
            : List<dynamic>.from(json["errors"]!.map((x) => x)),
        warnings: json["warnings"] == null
            ? []
            : List<dynamic>.from(json["warnings"]!.map((x) => x)),
        exceptions: json["exceptions"] == null
            ? []
            : List<dynamic>.from(json["exceptions"]!.map((x) => x)),
        validationErrors: json["validation_errors"] == null
            ? []
            : List<dynamic>.from(json["validation_errors"]!.map((x) => x)),
        dependencyErrors: json["dependency_errors"] == null
            ? []
            : List<dynamic>.from(json["dependency_errors"]!.map((x) => x)),
        uniquenessIssues: json["uniqueness_issues"] == null
            ? []
            : List<UniquenessIssue>.from(json["uniqueness_issues"]!
                .map((x) => UniquenessIssue.fromJson(x))),
        parsingIssues: json["parsing_issues"] == null
            ? []
            : List<dynamic>.from(json["parsing_issues"]!.map((x) => x)),
        mongoErrors: json["mongo_errors"] == null
            ? []
            : List<dynamic>.from(json["mongo_errors"]!.map((x) => x)),
        postgresErrors: json["postgres_errors"] == null
            ? []
            : List<dynamic>.from(json["postgres_errors"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "summary": summary?.toJson(),
        "errors":
            errors == null ? [] : List<dynamic>.from(errors!.map((x) => x)),
        "warnings":
            warnings == null ? [] : List<dynamic>.from(warnings!.map((x) => x)),
        "exceptions": exceptions == null
            ? []
            : List<dynamic>.from(exceptions!.map((x) => x)),
        "validation_errors": validationErrors == null
            ? []
            : List<dynamic>.from(validationErrors!.map((x) => x)),
        "dependency_errors": dependencyErrors == null
            ? []
            : List<dynamic>.from(dependencyErrors!.map((x) => x)),
        "uniqueness_issues": uniquenessIssues == null
            ? []
            : List<dynamic>.from(uniquenessIssues!.map((x) => x.toJson())),
        "parsing_issues": parsingIssues == null
            ? []
            : List<dynamic>.from(parsingIssues!.map((x) => x)),
        "mongo_errors": mongoErrors == null
            ? []
            : List<dynamic>.from(mongoErrors!.map((x) => x)),
        "postgres_errors": postgresErrors == null
            ? []
            : List<dynamic>.from(postgresErrors!.map((x) => x)),
      };
}

class UniquenessIssue {
  String? status;
  String? message;
  String? source;
  DateTime? timestamp;
  UniquenessResult? details;
  String? existingDocumentId;

  UniquenessIssue({
    this.status,
    this.message,
    this.source,
    this.timestamp,
    this.details,
    this.existingDocumentId,
  });

  UniquenessIssue copyWith({
    String? status,
    String? message,
    String? source,
    DateTime? timestamp,
    UniquenessResult? details,
    String? existingDocumentId,
  }) =>
      UniquenessIssue(
        status: status ?? this.status,
        message: message ?? this.message,
        source: source ?? this.source,
        timestamp: timestamp ?? this.timestamp,
        details: details ?? this.details,
        existingDocumentId: existingDocumentId ?? this.existingDocumentId,
      );

  factory UniquenessIssue.fromJson(Map<String, dynamic> json) =>
      UniquenessIssue(
        status: json["status"],
        message: json["message"],
        source: json["source"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        details: json["details"] == null
            ? null
            : UniquenessResult.fromJson(json["details"]),
        existingDocumentId: json["existing_document_id"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "source": source,
        "timestamp": timestamp?.toIso8601String(),
        "details": details?.toJson(),
        "existing_document_id": existingDocumentId,
      };
}

class UniquenessResult {
  bool? isUnique;
  String? status;
  String? message;
  ParsedData? existingDocument;

  UniquenessResult({
    this.isUnique,
    this.status,
    this.message,
    this.existingDocument,
  });

  UniquenessResult copyWith({
    bool? isUnique,
    String? status,
    String? message,
    ParsedData? existingDocument,
  }) =>
      UniquenessResult(
        isUnique: isUnique ?? this.isUnique,
        status: status ?? this.status,
        message: message ?? this.message,
        existingDocument: existingDocument ?? this.existingDocument,
      );

  factory UniquenessResult.fromJson(Map<String, dynamic> json) =>
      UniquenessResult(
        isUnique: json["is_unique"],
        status: json["status"],
        message: json["message"],
        existingDocument: json["existing_document"] == null
            ? null
            : ParsedData.fromJson(json["existing_document"]),
      );

  Map<String, dynamic> toJson() => {
        "is_unique": isUnique,
        "status": status,
        "message": message,
        "existing_document": existingDocument?.toJson(),
      };
}

class ParsedData {
  String? id;
  String? entityId;
  String? name;
  String? displayName;
  String? tenantId;
  String? tenantName;
  String? businessDomain;
  String? category;
  List<String>? tags;
  String? archivalStrategy;
  String? icon;
  String? colourTheme;
  int? version;
  String? status;
  String? type;
  String? description;
  String? tableName;
  String? naturalLanguage;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? entityStatus;
  List<dynamic>? changesDetected;
  String? iconType;
  String? iconContent;

  ParsedData({
    this.id,
    this.entityId,
    this.name,
    this.displayName,
    this.tenantId,
    this.tenantName,
    this.businessDomain,
    this.category,
    this.tags,
    this.archivalStrategy,
    this.icon,
    this.colourTheme,
    this.version,
    this.status,
    this.type,
    this.description,
    this.tableName,
    this.naturalLanguage,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.entityStatus,
    this.changesDetected,
    this.iconType,
    this.iconContent,
  });

  ParsedData copyWith({
    String? id,
    String? entityId,
    String? name,
    String? displayName,
    String? tenantId,
    String? tenantName,
    String? businessDomain,
    String? category,
    List<String>? tags,
    String? archivalStrategy,
    String? icon,
    String? colourTheme,
    int? version,
    String? status,
    String? type,
    String? description,
    String? tableName,
    String? naturalLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? entityStatus,
    List<dynamic>? changesDetected,
    String? iconType,
    String? iconContent,
  }) =>
      ParsedData(
        id: id ?? this.id,
        entityId: entityId ?? this.entityId,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        tenantId: tenantId ?? this.tenantId,
        tenantName: tenantName ?? this.tenantName,
        businessDomain: businessDomain ?? this.businessDomain,
        category: category ?? this.category,
        tags: tags ?? this.tags,
        archivalStrategy: archivalStrategy ?? this.archivalStrategy,
        icon: icon ?? this.icon,
        colourTheme: colourTheme ?? this.colourTheme,
        version: version ?? this.version,
        status: status ?? this.status,
        type: type ?? this.type,
        description: description ?? this.description,
        tableName: tableName ?? this.tableName,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        entityStatus: entityStatus ?? this.entityStatus,
        changesDetected: changesDetected ?? this.changesDetected,
        iconType: iconType ?? this.iconType,
        iconContent: iconContent ?? this.iconContent,
      );

  factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        id: json["_id"],
        entityId: json["entity_id"],
        name: json["name"],
        displayName: json["display_name"],
        tenantId: json["tenant_id"],
        tenantName: json["tenant_name"],
        businessDomain: json["business_domain"],
        category: json["category"],
        tags: json["tags"] == null
            ? []
            : List<String>.from(json["tags"]!.map((x) => x)),
        archivalStrategy: json["archival_strategy"],
        icon: json["icon"],
        colourTheme: json["colour_theme"],
        version: json["version"],
        status: json["status"],
        type: json["type"],
        description: json["description"],
        tableName: json["table_name"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        entityStatus: json["entity_status"],
        changesDetected: json["changes_detected"] == null
            ? []
            : List<dynamic>.from(json["changes_detected"]!.map((x) => x)),
        iconType: json["icon_type"],
        iconContent: json["icon_content"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "entity_id": entityId,
        "name": name,
        "display_name": displayName,
        "tenant_id": tenantId,
        "tenant_name": tenantName,
        "business_domain": businessDomain,
        "category": category,
        "tags": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "archival_strategy": archivalStrategy,
        "icon": icon,
        "colour_theme": colourTheme,
        "version": version,
        "status": status,
        "type": type,
        "description": description,
        "table_name": tableName,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "entity_status": entityStatus,
        "changes_detected": changesDetected == null
            ? []
            : List<dynamic>.from(changesDetected!.map((x) => x)),
        "icon_type": iconType,
        "icon_content": iconContent,
      };
}

class ValidationResult {
  List<dynamic>? structureErrors;
  List<dynamic>? requiredFieldErrors;
  List<dynamic>? dataTypeErrors;
  List<dynamic>? customErrors;

  ValidationResult({
    this.structureErrors,
    this.requiredFieldErrors,
    this.dataTypeErrors,
    this.customErrors,
  });

  ValidationResult copyWith({
    List<dynamic>? structureErrors,
    List<dynamic>? requiredFieldErrors,
    List<dynamic>? dataTypeErrors,
    List<dynamic>? customErrors,
  }) =>
      ValidationResult(
        structureErrors: structureErrors ?? this.structureErrors,
        requiredFieldErrors: requiredFieldErrors ?? this.requiredFieldErrors,
        dataTypeErrors: dataTypeErrors ?? this.dataTypeErrors,
        customErrors: customErrors ?? this.customErrors,
      );

  factory ValidationResult.fromJson(Map<String, dynamic> json) =>
      ValidationResult(
        structureErrors: json["structure_errors"] == null
            ? []
            : List<dynamic>.from(json["structure_errors"]!.map((x) => x)),
        requiredFieldErrors: json["required_field_errors"] == null
            ? []
            : List<dynamic>.from(json["required_field_errors"]!.map((x) => x)),
        dataTypeErrors: json["data_type_errors"] == null
            ? []
            : List<dynamic>.from(json["data_type_errors"]!.map((x) => x)),
        customErrors: json["custom_errors"] == null
            ? []
            : List<dynamic>.from(json["custom_errors"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "structure_errors": structureErrors == null
            ? []
            : List<dynamic>.from(structureErrors!.map((x) => x)),
        "required_field_errors": requiredFieldErrors == null
            ? []
            : List<dynamic>.from(requiredFieldErrors!.map((x) => x)),
        "data_type_errors": dataTypeErrors == null
            ? []
            : List<dynamic>.from(dataTypeErrors!.map((x) => x)),
        "custom_errors": customErrors == null
            ? []
            : List<dynamic>.from(customErrors!.map((x) => x)),
      };
}
