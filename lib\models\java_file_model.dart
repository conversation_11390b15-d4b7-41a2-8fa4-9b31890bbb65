import 'dart:convert';

JavaFileModel javaFileModelFromJson(String str) => 
    JavaFileModel.fromJson(json.decode(str));

String javaFileModelToJson(JavaFileModel data) => 
    json.encode(data.toJson());

class JavaFileModel {
  String? fileName;
  String? fileType;
  Solution? solution;

  JavaFileModel({
    this.fileName,
    this.fileType,
    this.solution,
  });

  JavaFileModel copyWith({
    String? fileName,
    String? fileType,
    Solution? solution,
  }) =>
      JavaFileModel(
        fileName: fileName ?? this.fileName,
        fileType: fileType ?? this.fileType,
        solution: solution ?? this.solution,
      );

  factory JavaFileModel.fromJson(Map<String, dynamic> json) => 
      JavaFileModel(
        fileName: json["fileName"],
        fileType: json["fileType"],
        solution: json["solution"] == null 
            ? null 
            : Solution.fromJson(json["solution"]),
      );

  Map<String, dynamic> toJson() => {
        "fileName": fileName,
        "fileType": fileType,
        "solution": solution?.toJson(),
      };
}

class Solution {
  Java? java;

  Solution({
    this.java,
  });

  Solution copyWith({
    Java? java,
  }) =>
      Solution(
        java: java ?? this.java,
      );

  factory Solution.fromJson(Map<String, dynamic> json) => 
      Solution(
        java: json["java"] == null 
            ? null 
            : Java.fromJson(json["java"]),
      );

  Map<String, dynamic> toJson() => {
        "java": java?.toJson(),
      };
}

class Java {
  List<JavaLine>? lines;

  Java({
    this.lines,
  });

  Java copyWith({
    List<JavaLine>? lines,
  }) =>
      Java(
        lines: lines ?? this.lines,
      );

  factory Java.fromJson(Map<String, dynamic> json) => 
      Java(
        lines: json["lines"] == null 
            ? [] 
            : List<JavaLine>.from(
                json["lines"]!.map((x) => JavaLine.fromJson(x))
              ),
      );

  Map<String, dynamic> toJson() => {
        "lines": lines == null 
            ? [] 
            : List<dynamic>.from(lines!.map((x) => x.toJson())),
      };
}

class JavaLine {
  int? lineNo;
  String? content;

  JavaLine({
    this.lineNo,
    this.content,
  });

  JavaLine copyWith({
    int? lineNo,
    String? content,
  }) =>
      JavaLine(
        lineNo: lineNo ?? this.lineNo,
        content: content ?? this.content,
      );

  factory JavaLine.fromJson(Map<String, dynamic> json) => 
      JavaLine(
        lineNo: json["line_no"],
        content: json["content"],
      );

  Map<String, dynamic> toJson() => {
        "line_no": lineNo,
        "content": content,
      };
}
