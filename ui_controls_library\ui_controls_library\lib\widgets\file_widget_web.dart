import 'dart:convert';
// ignore: deprecated_member_use
import 'dart:html' as html;
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import '../utils/callback_interpreter.dart';
import 'utils/file_widget_json_parser.dart';
import 'components/file_widget_components.dart';

/// Web-specific implementation of FileWidgetImplementation
class FileWidgetImplementation extends StatefulWidget {
  // Basic properties
  final bool isRequired;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final FileType fileType;
  final int? maxFileSizeBytes;
  final int? maxFiles;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String buttonText;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool showFileName;
  final bool showFileSize;
  final bool showFileType;
  final bool showClearButton;
  final bool showPreview;
  final bool uploadImmediately;
  final bool showProgressBar;
  final bool allowDragDrop;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callbacks
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function()? onClear;
  final Function(List<PlatformFile>)? onUpload;
  final Function(PlatformFile)? onViewFile;
  final Function(PlatformFile)? onOpenFile;
  final Function()? onCancelUpload;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // File-specific JSON configuration
  final bool useJsonFileHandling;
  final Map<String, dynamic>? fileHandlingConfig;

  const FileWidgetImplementation({
    super.key,
    this.isRequired = false,
    this.allowMultiple = false,
    this.allowedExtensions,
    this.fileType = FileType.any,
    this.maxFileSizeBytes,
    this.maxFiles,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.buttonText = 'Choose File',
    this.showIcon = true,
    this.icon = Icons.upload_file,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showFileName = true,
    this.showFileSize = true,
    this.showFileType = true,
    this.showClearButton = true,
    this.showPreview = false,
    this.uploadImmediately = false,
    this.showProgressBar = false,
    this.allowDragDrop = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onFilesSelected,
    this.onClear,
    this.onUpload,
    this.onViewFile,
    this.onOpenFile,
    this.onCancelUpload,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    this.useJsonFileHandling = false,
    this.fileHandlingConfig,
  });

  @override
  State<FileWidgetImplementation> createState() =>
      _FileWidgetImplementationState();
}

// Enum for file upload states
enum FileUploadState { defaultState, uploading, uploaded, completed }

class _FileWidgetImplementationState extends State<FileWidgetImplementation> {
  List<PlatformFile> _selectedFiles = [];
  String? _errorText;
  bool _isDragging = false;
  double _uploadProgress = 0.0;
  bool _isUploading = false;
  FileUploadState _uploadState = FileUploadState.defaultState;
  bool _isHovered = false;
  bool _hasFocus = false;

  final hoverIconColor = const Color(0xFF0058FF);
  final defaultIconColor = const Color(0xFFCCCCCC);

  @override
  void initState() {
    super.initState();
    _errorText = widget.errorText;
  }

  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }
    });
  }

  Future<void> _pickFiles() async {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      final result = await FilePicker.platform.pickFiles(
        type: widget.fileType,
        allowMultiple: widget.allowMultiple,
        allowedExtensions:
            widget.fileType == FileType.custom
                ? widget.allowedExtensions
                : null,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFiles = result.files;
          _errorText = null;
        });

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        // Start upload automatically
        _uploadFiles();
      }
    } catch (e) {
      setState(() {
        _errorText = 'Error picking files: $e';
      });
    }
  }

  void _clearFiles() {
    setState(() {
      _selectedFiles = [];
      _errorText = widget.errorText;
      _uploadProgress = 0.0;
      _isUploading = false;
      _uploadState = FileUploadState.defaultState;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }
  }

  void _cancelUpload() {
    if (!_isUploading) return;

    setState(() {
      _isUploading = false;
      _uploadProgress = 0.0;
      _uploadState = FileUploadState.defaultState;
    });

    if (widget.onCancelUpload != null) {
      widget.onCancelUpload!();
    }
  }

  Future<void> _uploadFiles() async {
    if (_selectedFiles.isEmpty || widget.isDisabled || widget.isReadOnly) {
      return;
    }

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _uploadState = FileUploadState.uploading;
    });

    try {
      // Simulate upload progress
      for (int i = 0; i <= 20; i++) {
        if (!_isUploading) return;

        await Future.delayed(const Duration(milliseconds: 100));

        if (!_isUploading) return;

        final progress = i / 20;
        setState(() {
          _uploadProgress = progress;
        });
      }

      // Show uploaded state (100% complete)
      setState(() {
        _uploadState = FileUploadState.uploaded;
      });

      // Show uploaded state briefly before finishing
      await Future.delayed(const Duration(milliseconds: 1500));

      if (!_isUploading) return;

      setState(() {
        _isUploading = false;
        _uploadState = FileUploadState.completed;
      });

      if (widget.onUpload != null) {
        widget.onUpload!(_selectedFiles);
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
        _errorText = 'Upload failed: $e';
      });
    }
  }

  String _getMimeType(String? extension) {
    if (extension == null) return 'application/octet-stream';

    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'svg':
        return 'image/svg+xml';
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/x-msvideo';
      case 'mov':
        return 'video/quicktime';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/vnd.rar';
      case 'txt':
        return 'text/plain';
      case 'html':
        return 'text/html';
      case 'css':
        return 'text/css';
      case 'js':
        return 'application/javascript';
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';
      default:
        return 'application/octet-stream';
    }
  }

  Future<void> _openFileInNewWindow(PlatformFile file) async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening file: ${file.name}'),
          duration: const Duration(seconds: 2),
        ),
      );

      if (file.bytes != null) {
        // Create blob URL from file bytes with proper MIME type
        final mimeType = _getMimeType(file.extension);
        final blob = html.Blob([file.bytes!], mimeType);
        final url = html.Url.createObjectUrlFromBlob(blob);

        // Open in new tab/window
        html.window.open(url, '_blank');

        // Clean up the blob URL after a delay
        Future.delayed(const Duration(seconds: 5), () {
          html.Url.revokeObjectUrl(url);
        });

        print('Successfully opened file in new tab: ${file.name}');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Cannot open file: ${file.name} (no file data available)',
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening file: $e'),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
      print('Error opening file: $e');
    }
  }

  void _viewFile(PlatformFile file) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return FilePreviewDialog(
          file: file,
          themeColor: const Color(0xFF0058FF),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: Colors.black87),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Color _getFileIconColor(String? extension) {
    if (extension == null) return const Color(0xFF666666);

    switch (extension.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFD32F2F);
      case 'doc':
      case 'docx':
        return const Color(0xFF1976D2);
      case 'xls':
      case 'xlsx':
        return const Color(0xFF388E3C);
      case 'ppt':
      case 'pptx':
        return const Color(0xFFFF5722);
      case 'txt':
        return const Color(0xFF757575);
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return const Color(0xFF9C27B0);
      case 'mp4':
      case 'avi':
      case 'mov':
        return const Color(0xFFE91E63);
      case 'mp3':
      case 'wav':
      case 'flac':
        return const Color(0xFF4CAF50);
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF795548);
      case 'html':
        return const Color(0xFFFF5722);
      case 'css':
        return const Color(0xFF2196F3);
      case 'js':
        return const Color(0xFFFFC107);
      case 'json':
        return const Color(0xFF607D8B);
      default:
        return const Color(0xFF666666);
    }
  }

  IconData _getFileIcon(String? extension) {
    if (extension == null) return Icons.insert_drive_file;

    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.folder_zip;
      case 'html':
        return Icons.web;
      case 'css':
        return Icons.style;
      case 'js':
        return Icons.code;
      case 'json':
        return Icons.data_object;
      default:
        return Icons.insert_drive_file;
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) return 18.0;
    if (screenWidth >= 1440) return 16.0;
    if (screenWidth >= 1280) return 14.0;
    if (screenWidth >= 768) return 12.0;
    return 10.0;
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) return 56.0;
    if (screenWidth >= 1440) return 48.0;
    if (screenWidth >= 1280) return 40.0;
    if (screenWidth >= 768) return 40.0;
    return 40.0;
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 1440)
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0);
    if (screenWidth >= 1280)
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0);
    if (screenWidth >= 768)
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 2.0);
    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 1.0);
  }

  double _getResponsiveIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) return 22.0;
    if (screenWidth >= 1440) return 20.0;
    if (screenWidth >= 1280) return 18.0;
    if (screenWidth >= 768) return 15.0;
    return 15.0;
  }

  Widget _buildDragDropArea(Widget child) {
    if (!widget.allowDragDrop) return child;

    return DragTarget<List<PlatformFile>>(
      onWillAccept: (data) => !widget.isDisabled && !widget.isReadOnly,
      onAccept: (files) {
        setState(() {
          _selectedFiles = files;
          _isDragging = false;
        });
        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(files);
        }
      },
      onLeave: (data) {
        setState(() {
          _isDragging = false;
        });
      },
      builder: (context, candidateData, rejectedData) {
        return Container(
          decoration: BoxDecoration(
            border:
                _isDragging ? Border.all(color: Colors.blue, width: 2) : null,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    switch (_uploadState) {
      case FileUploadState.defaultState:
        content = _buildDefaultState();
        break;
      case FileUploadState.uploading:
        content = _buildUploadingState();
        break;
      case FileUploadState.uploaded:
        content = _buildUploadedState();
        break;
      case FileUploadState.completed:
        content = _buildCompletedState();
        break;
    }

    final dragDropContent = _buildDragDropArea(content);

    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: dragDropContent,
            )
            : dragDropContent;

    return Container(
      width: widget.width,
      margin: widget.margin,
      child: shadowWidget,
    );
  }

  Widget _buildDefaultState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: _getResponsiveHeight(context),
          child: ElevatedButton.icon(
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _pickFiles,
            icon:
                widget.showIcon && widget.icon != null
                    ? Icon(
                      widget.icon,
                      color: Colors.white,
                      size: _getResponsiveIconSize(context),
                    )
                    : const SizedBox.shrink(),
            label: Text(
              'Upload ',
              // style: TextStyle(
              //   fontSize: _getResponsiveFontSize(context),
              //   fontWeight: FontWeight.w400,
              //   color: Colors.white,
              // ),
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                //color: effectiveTextColor.withOpacity(0.6),
                color: Colors.white,
                fontSize: _getResponsiveValueFontSize(context),
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0058FF),
              padding: _getResponsivePadding(context),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4.0),
              ),
              elevation: 0,
            ),
          ),
        ),
        if (_errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            _errorText!,
            style: TextStyle(
              color: Colors.red,
              fontSize: _getResponsiveFontSize(context) * 0.8,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUploadingState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 12.0,
                                left: 12.0,
                              ),
                              child: Text(
                                'Uploading (${_selectedFiles.length})',
                                style: TextStyle(
                                  fontSize:
                                      _getResponsiveFontSize(context) * 0.9,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF333333),
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: _cancelUpload,
                              child: Container(
                                padding: const EdgeInsets.only(
                                  right: 12.0,
                                  top: 5.0,
                                ),
                                child: Icon(
                                  Icons.close,
                                  size: _getResponsiveIconSize(context),
                                  color: defaultIconColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 3),
                        LinearProgressIndicator(
                          value: _uploadProgress,
                          backgroundColor: Colors.grey.shade300,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.blue,
                          ),
                          minHeight: _getResponsiveProgressBarHeight(context),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 12.0,
                            left: 12.0,
                            right: 12.0,
                          ),
                          child: Text(
                            'Uploaded 100%',
                            style: TextStyle(
                              fontSize: _getResponsiveFontSize(context) * 0.9,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF999999),
                            ),
                          ),
                        ),
                        const SizedBox(height: 3),
                        LinearProgressIndicator(
                          value: 1.0,
                          backgroundColor: Colors.green.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.green.shade600,
                          ),
                          minHeight: _getResponsiveProgressBarHeight(context),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompletedState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    if (_selectedFiles.isEmpty) {
      return _buildDefaultState();
    }

    final file = _selectedFiles.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              padding: _getResponsivePadding(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
              ),
              child: Row(
                children: [
                  // File type icon
                  Icon(
                    _getFileIcon(file.extension),
                    size: _getResponsiveIconSize(context),
                    color: _getFileIconColor(file.extension),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      file.name,
                      style: TextStyle(
                        fontSize: _getResponsiveFontSize(context) * 0.9,
                        color: const Color(0xFF333333),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Navigate icon (opens file in new window)
                  GestureDetector(
                    onTap: () => _openFileInNewWindow(file),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      // child: Icon(
                      //   Icons.open_in_new,
                      //   size: _getResponsiveIconSize(context),
                      //   color: _isHovered ? hoverIconColor : defaultIconColor,
                      // ),
                      child: SvgPicture.asset(
                        _isHovered
                            ? 'assets/images/maxmize-hover.svg'
                            : 'assets/images/maxmize.svg',
                        package: 'ui_controls_library',
                        width: _getResponsiveIconSize(context),
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  // Eye icon (views the file)
                  GestureDetector(
                    onTap: () => _viewFile(file),
                    child: Container(
                      // padding: const EdgeInsets.all(4),
                      // child: Icon(
                      //   Icons.visibility,
                      //   size: _getResponsiveIconSize(context),
                      //   color: _isHovered ? hoverIconColor : defaultIconColor,
                      // ),
                      child: SvgPicture.asset(
                        _isHovered
                            ? 'assets/images/visibility-on.svg'
                            : 'assets/images/visibility.svg',
                        package: 'ui_controls_library',
                        //width: _getResponsiveIconSize(context),
                      ),
                    ),
                  ),
                  const SizedBox(width: 5),
                  // Delete icon (deletes file and returns to default state)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedFiles.clear();
                        _uploadState = FileUploadState.defaultState;
                        _uploadProgress = 0.0;
                        _isUploading = false;
                      });
                      if (widget.onClear != null) {
                        widget.onClear!();
                      }
                    },
                    child: Container(
                      // padding: const EdgeInsets.all(4),
                      // child: Icon(
                      //   Icons.delete_outline,
                      //   size: _getResponsiveIconSize(context),
                      //   color: _isHovered ? hoverIconColor : defaultIconColor,
                      // ),
                      child: SvgPicture.asset(
                        _isHovered
                            ? 'assets/images/trash-hover.svg'
                            : 'assets/images/trash.svg',
                        package: 'ui_controls_library',
                        //width: _getResponsiveIconSize(context),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

double _getResponsiveProgressBarHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 8.5; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 8.5; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 4.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 4.0; // Small (768-1024px)
  } else {
    return 4.0; // Default for very small screens
  }
}

/// File Preview Dialog for web-optimized file content viewing
class FilePreviewDialog extends StatefulWidget {
  final PlatformFile file;
  final Color themeColor;

  const FilePreviewDialog({
    super.key,
    required this.file,
    this.themeColor = const Color(0xFF0058FF),
  });

  @override
  State<FilePreviewDialog> createState() => _FilePreviewDialogState();
}

class _FilePreviewDialogState extends State<FilePreviewDialog> {
  double _zoomLevel = 1.0;
  int _currentPage = 1;
  final int _totalPages = 5; // Simulated total pages
  bool _isLoading = true;
  bool _hasError = false;
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  @override
  void initState() {
    super.initState();

    // Simulate document loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = false; // For demo, we'll show content anyway
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _closeDialog() {
    Navigator.of(context).pop();
  }

  void _zoomIn() {
    setState(() {
      _zoomLevel = math.min(3.0, _zoomLevel + 0.25);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = math.max(0.5, _zoomLevel - 0.25);
    });
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  void _toggleSearch() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        _searchController.clear();
      }
    });
  }

  void _downloadDocument() {
    try {
      if (widget.file.bytes != null) {
        // Web download implementation using actual file bytes
        final blob = html.Blob([widget.file.bytes!]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor =
            html.document.createElement('a') as html.AnchorElement
              ..href = url
              ..style.display = 'none'
              ..download = widget.file.name;
        html.document.body?.children.add(anchor);
        anchor.click();
        html.document.body?.children.remove(anchor);
        html.Url.revokeObjectUrl(url);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Downloaded ${widget.file.name}'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No file data available for download'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Download failed: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Widget _buildWebToolbar() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Left side - Menu icon and file name
          IconButton(
            icon: const Icon(Icons.menu, size: 18),
            onPressed: () {},
            tooltip: 'Menu',
            color: Colors.grey.shade600,
          ),
          Expanded(
            flex: 3,
            child: Text(
              widget.file.name.length > 12
                  ? '${widget.file.name.substring(0, 12)}...'
                  : widget.file.name,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Page navigation
          Text(
            '$_currentPage',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
          ),
          const SizedBox(width: 4),
          Text(
            '/',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade500),
          ),
          const SizedBox(width: 4),
          Text(
            '$_totalPages',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
          ),

          const SizedBox(width: 16),

          // Zoom controls
          IconButton(
            icon: const Icon(Icons.remove, size: 16),
            onPressed: _zoomOut,
            tooltip: 'Zoom Out',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade400),
              borderRadius: BorderRadius.circular(2),
            ),
            child: Text(
              '${(_zoomLevel * 100).round()}%',
              style: const TextStyle(fontSize: 11),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.add, size: 16),
            onPressed: _zoomIn,
            tooltip: 'Zoom In',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),

          const SizedBox(width: 8),

          // Right side actions
          IconButton(
            icon: const Icon(Icons.file_download, size: 18),
            onPressed: _downloadDocument,
            tooltip: 'Download',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.print, size: 18),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Print ${widget.file.name}'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            tooltip: 'Print',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 18),
            onPressed: _closeDialog,
            tooltip: 'Close',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, size: 18),
            onPressed: () {
              // Show more options menu
              _showMoreOptions();
            },
            tooltip: 'More options',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  void _showMoreOptions() {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        MediaQuery.of(context).size.width - 200,
        50,
        MediaQuery.of(context).size.width,
        100,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.search, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Search'),
            ],
          ),
          onTap: _toggleSearch,
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.fullscreen, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Full Screen'),
            ],
          ),
          onTap: () {
            // Toggle full screen
          },
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Document Info'),
            ],
          ),
          onTap: () {
            // Show document info
          },
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    if (!_showSearchBar) return const SizedBox.shrink();

    return Container(
      height: 40,
      color: Colors.grey.shade100,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search in document...',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 12),
              onSubmitted: (value) {
                // Implement search functionality
              },
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.arrow_upward, size: 16),
            onPressed: () {
              // Previous search result
            },
            tooltip: 'Previous',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_downward, size: 16),
            onPressed: () {
              // Next search result
            },
            tooltip: 'Next',
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 16),
            onPressed: _toggleSearch,
            tooltip: 'Close Search',
          ),
        ],
      ),
    );
  }

  Widget _buildFileContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Failed to load document',
              style: TextStyle(color: Colors.grey.shade700, fontSize: 16),
            ),
          ],
        ),
      );
    }

    // Handle different file types
    final extension = widget.file.extension?.toLowerCase();

    if (extension == 'txt' && widget.file.bytes != null) {
      // Display text file content
      return _buildTextFileContent();
    } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].contains(extension) &&
        widget.file.bytes != null) {
      // Display image file content
      return _buildImageFileContent();
    } else {
      // Display generic document preview
      return _buildGenericDocumentContent();
    }
  }

  Widget _buildTextFileContent() {
    try {
      final content = utf8.decode(widget.file.bytes!);
      return Container(
        color: Colors.grey.shade200,
        child: Center(
          child: Transform.scale(
            scale: _zoomLevel,
            child: Container(
              width: 400,
              height: 500,
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(51),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Document header
                    Text(
                      widget.file.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Size: ${_formatFileSize(widget.file.size)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // File content
                    Expanded(
                      child: SingleChildScrollView(
                        child: Text(
                          content,
                          style: const TextStyle(fontSize: 12, height: 1.5),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    } catch (e) {
      return _buildGenericDocumentContent();
    }
  }

  Widget _buildImageFileContent() {
    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Transform.scale(
          scale: _zoomLevel,
          child: Container(
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Image header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.file.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Size: ${_formatFileSize(widget.file.size)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Image content
                Container(
                  constraints: const BoxConstraints(
                    maxWidth: 600,
                    maxHeight: 400,
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Image.memory(
                    widget.file.bytes!,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 200,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image,
                                size: 48,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Cannot display image',
                                style: TextStyle(color: Colors.grey.shade600),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGenericDocumentContent() {
    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Transform.scale(
          scale: _zoomLevel,
          child: Container(
            width: 400,
            height: 500,
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Document header
                  Text(
                    widget.file.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Page $_currentPage',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const SizedBox(height: 20),

                  // Document content
                  const Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                            style: TextStyle(fontSize: 12, height: 1.5),
                          ),
                          SizedBox(height: 16),

                          Text('• Morbi viverra semper lorem nec molestie.'),
                          Text(
                            '• Maecenas tincidunt est efficitur ligula euismod.',
                          ),
                          Text('• Sit amet ornare est vulputate.'),

                          SizedBox(height: 20),

                          Text(
                            'Vestibulum neque massa, scelerisque sit amet ligula eu, congue molestie mi. Praesent ut varius sem.',
                            style: TextStyle(fontSize: 12, height: 1.5),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: Container(
        width: screenSize.width * 0.8,
        height: screenSize.height * 0.8,
        constraints: const BoxConstraints(maxWidth: 800, maxHeight: 600),
        child: Column(
          children: [
            _buildWebToolbar(),
            _buildSearchBar(),
            Expanded(child: _buildFileContent()),
          ],
        ),
      ),
    );
  }
}
