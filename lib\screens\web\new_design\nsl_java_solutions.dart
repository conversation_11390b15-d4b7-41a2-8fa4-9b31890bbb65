import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../models/nsl_java_solution_model.dart';
import '../../../services/nsl_java_solution_service.dart';
import '../../../utils/logger.dart';
import '../../../utils/font_manager.dart';
import '../../../utils/responsive_font_sizes.dart';
import 'nsl_java_content_parsing_screen.dart';

class NslJavaSolutionsScreen extends StatefulWidget {
  const NslJavaSolutionsScreen({Key? key}) : super(key: key);

  @override
  State<NslJavaSolutionsScreen> createState() => _NslJavaSolutionsScreenState();
}

class _NslJavaSolutionsScreenState extends State<NslJavaSolutionsScreen> {
  final NslJavaSolutionService _solutionService = NslJavaSolutionService();
  List<NslJavaSolution> _solutions = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSolutions();
  }

  Future<void> _loadSolutions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final solutionModel =
          await _solutionService.getActiveGlobalObjectivesWithFallback();
      setState(() {
        _solutions = solutionModel.data ?? [];
        _isLoading = false;
      });
      Logger.info('Loaded ${_solutions.length} NSL Java solutions');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load solutions: ${e.toString()}';
      });
      Logger.error('Error loading NSL Java solutions: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_errorMessage != null) {
      return _buildError();
    }
    if (_solutions.isEmpty) {
      return _buildEmpty();
    }
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final availableWidth = constraints.maxWidth;
            const cardWidth = 120.0;
            const cardHeight = 160.0;
            const minSpacing = AppSpacing.md;
            int itemsPerRow =
                ((availableWidth + minSpacing) / (cardWidth + minSpacing))
                    .floor();
            if (itemsPerRow < 1) itemsPerRow = 1;
            int rowCount = (_solutions.length / itemsPerRow).ceil();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: List.generate(rowCount, (rowIndex) {
                int startIndex = rowIndex * itemsPerRow;
                int endIndex = (startIndex + itemsPerRow <= _solutions.length)
                    ? startIndex + itemsPerRow
                    : _solutions.length;
                List<NslJavaSolution> rowSolutions =
                    _solutions.sublist(startIndex, endIndex);

                return Padding(
                  padding: const EdgeInsets.only(bottom: AppSpacing.lg),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: rowSolutions.asMap().entries.map((entry) {
                      final idx = entry.key;
                      final solution = entry.value;
                      double spacing =
                          idx < rowSolutions.length - 1 ? minSpacing : 0;

                      return Padding(
                        padding: EdgeInsets.only(right: spacing),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => _onSolutionTap(solution),
                            child: _buildSolutionCard(
                                solution, cardWidth, cardHeight),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                );
              }),
            );
          },
        ),
      ),
    );
  }

  Widget _buildError() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text('Error loading solutions',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.red[700],
              )),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(_errorMessage!,
                textAlign: TextAlign.center,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                )),
          ),
        ],
      ),
    );
  }

  Widget _buildEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('No Solutions Found',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[600],
              )),
        ],
      ),
    );
  }

  void _onSolutionTap(NslJavaSolution solution) async {
    if (solution.goid == null) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_go_id', solution.goid!);
      Navigator.of(context).pop();
      Provider.of<WebHomeProvider>(context, listen: false).currentScreenIndex =
          ScreenConstants.nslJavaContentParsingScreen;
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Failed to load content: \$e'),
            backgroundColor: Colors.red),
      );
    }
  }

  Widget _buildSolutionCard(
      NslJavaSolution solution, double width, double height) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: width,
          height: height,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            child: Image.asset(
              'assets/images/solution-placeholder.png',
              width: width,
              height: height,
              fit: BoxFit.contain,
            ),
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        SizedBox(
          width: width,
          child: Text(
            solution.name ?? 'Unnamed Solution',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: width,
          child: Text(
            solution.goid ?? 'N/A',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s10,
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
