import 'package:dio/dio.dart';
import 'package:nsl/utils/navigation_service.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';
import 'auth_service.dart';

/// A singleton Dio client service with default configuration
class DioClient {
  // Singleton instance
  static final DioClient _instance = DioClient._internal();

  // Dio instance
  late final Dio dio;

  /// Factory constructor to return the same instance
  factory DioClient() {
    return _instance;
  }

  /// Private constructor to initialize the Dio instance
  DioClient._internal() {
    Logger.info('Initializing DioClient');

    dio = Dio(
      BaseOptions(
        connectTimeout: AppConstants.connectionTimeout,
        receiveTimeout: AppConstants.receiveTimeout,
        sendTimeout: AppConstants.sendTimeout,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );

    // Add interceptors
    _addInterceptors(dio);

    Logger.info('DioClient initialized successfully');
  }

  /// Add interceptors to the Dio instance
  void _addInterceptors(Dio dioInstance) {
    const int maxTokenRefreshAttempts = 3;
    const String retryCountKey = 'token_refresh_attempts';

    // Logging interceptor (your original)
    dioInstance.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: false,
      error: true,
      request: false,
      requestHeader: false,
      responseHeader: false,
      logPrint: (object) {
        Logger.debug('Dio: $object');
      },
    ));

    dioInstance.interceptors.add(
      InterceptorsWrapper(
        onResponse: (response, handler) async {
          // Optionally keep your logic here, but typically 401 will trigger onError, not onResponse
          if (response.statusCode == 401) {
            Logger.info(
                'Received 401 Unauthorized in response, attempting to refresh token');

            // Get per-request retry count
            final options = response.requestOptions;
            int retryCount = (options.extra[retryCountKey] as int?) ?? 0;
            if (retryCount < maxTokenRefreshAttempts) {
              try {
                final authService = AuthService();
                final newToken = await authService.getValidToken();

                if (newToken != null) {
                  Logger.info('Token refreshed successfully, retrying request');
                  options.headers['Authorization'] = 'Bearer $newToken';
                  options.extra[retryCountKey] = retryCount + 1;
                  final newResponse = await dioInstance.fetch(options);
                  return handler.resolve(newResponse);
                } else {
                  Logger.error('Failed to refresh token');
                }
              } catch (refreshError) {
                Logger.error('Error refreshing token: $refreshError');
              }
            } else {
              Logger.error(
                  'Max token refresh attempts exceeded! Redirecting to login.');
              await AuthService().logout();
              // Place navigation to login here, if needed outside this class.
            }
          }
          // Otherwise continue as normal
          return handler.next(response);
        },
        onError: (DioException e, handler) async {
          if (e.response?.statusCode == 401) {
            Logger.info(
                'Received 401 Unauthorized, attempting to refresh token');

            final options = e.requestOptions;
            int retryCount = (options.extra[retryCountKey] as int?) ?? 0;

            if (retryCount < maxTokenRefreshAttempts) {
              try {
                final authService = AuthService();
                final newToken = await authService.getValidToken();
                if (newToken != null) {
                  Logger.info('Token refreshed successfully, retrying request');
                  options.headers['Authorization'] = 'Bearer $newToken';
                  options.extra[retryCountKey] = retryCount + 1;
                  final response = await dioInstance.fetch(options);
                  return handler.resolve(response);
                } else {
                  Logger.error('Failed to refresh token');
                }
              } catch (refreshError) {
                Logger.error('Error refreshing token: $refreshError');
              }
            } else {
              Logger.error(
                  'Max token refresh attempts exceeded! Redirecting to login.');
              NavigationService.navigateToLogin();
              // Place navigation to login here, if needed outside this class.
            }
          }

          Logger.error('Dio Error: ${e.message}', stackTrace: e.stackTrace);
          return handler.next(e);
        },
      ),
    );
  }

  /// Get the Dio instance
  Dio get client => dio;

  /// Reset the Dio instance (useful for testing)
  void reset() {
    Logger.info('Resetting DioClient');
    dio.close(force: true);

    dio = Dio(
      BaseOptions(
        connectTimeout: AppConstants.connectionTimeout,
        receiveTimeout: AppConstants.receiveTimeout,
        sendTimeout: AppConstants.sendTimeout,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );

    // Re-add interceptors using the common method
    _addInterceptors(dio);

    Logger.info('DioClient reset successfully');
  }
}
